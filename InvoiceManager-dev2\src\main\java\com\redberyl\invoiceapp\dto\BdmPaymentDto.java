package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class BdmPaymentDto extends BaseDto {
    private Long id;

    @NotNull(message = "Invoice ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long invoiceId;
    // Remove circular reference to avoid compilation issues
    // private InvoiceDto invoice;

    @NotNull(message = "BDM ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long bdmId;
    private BdmDto bdm;

    @NotNull(message = "Amount is required")
    @Positive(message = "Amount must be positive")
    private BigDecimal amount;

    private LocalDate paidOn;
    private Boolean isPaid;
}
