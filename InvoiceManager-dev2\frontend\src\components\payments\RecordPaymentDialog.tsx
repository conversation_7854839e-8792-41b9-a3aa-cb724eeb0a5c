import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { paymentService, type CreatePaymentRequest } from "@/services/paymentService";

interface InvoiceData {
  id: string;
  client: string;
  amount: string;
  databaseId?: number;
}

interface RecordPaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPaymentRecorded: (payment: any) => void;
  invoiceData?: InvoiceData;
}

const RecordPaymentDialog: React.FC<RecordPaymentDialogProps> = ({
  open,
  onOpenChange,
  onPaymentRecorded,
  invoiceData,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [formData, setFormData] = useState({
    invoiceId: "",
    client: "",
    amount: "",
    method: "BANK_TRANSFER",
    status: "Completed",
    notes: "",
  });

  // Populate form data when invoice data is provided
  useEffect(() => {
    if (invoiceData && open) {
      setFormData(prev => ({
        ...prev,
        invoiceId: invoiceData.id,
        client: invoiceData.client,
        amount: invoiceData.amount.replace(/[^0-9.]/g, ''), // Remove currency symbols
      }));
    }
  }, [invoiceData, open]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form
      if (!formData.amount || !date) {
        toast.error("Please fill in all required fields");
        setIsSubmitting(false);
        return;
      }

      // Validate invoice ID (use database ID if available, otherwise try to parse the display ID)
      let invoiceId: number;
      if (invoiceData?.databaseId) {
        invoiceId = invoiceData.databaseId;
      } else {
        toast.error("Invoice ID is required to record payment");
        setIsSubmitting(false);
        return;
      }

      // Prepare payment data for API
      const paymentData: CreatePaymentRequest = {
        invoiceId: invoiceId,
        amountReceived: parseFloat(formData.amount),
        receivedOn: format(date, "yyyy-MM-dd"),
        paymentMode: formData.method,
        referenceNumber: formData.notes || undefined,
      };

      console.log("Creating payment with data:", paymentData);

      // Call the payment service
      const createdPayment = await paymentService.createPayment(paymentData);

      console.log("Payment created successfully:", createdPayment);

      // Create display object for callback
      const displayPayment = {
        id: createdPayment.id,
        invoiceId: formData.invoiceId,
        client: formData.client,
        amount: `$${formData.amount}`,
        date: format(date, "yyyy-MM-dd"),
        method: formData.method,
        status: "Completed",
        notes: formData.notes,
      };

      onPaymentRecorded(displayPayment);

      // Reset form
      setFormData({
        invoiceId: "",
        client: "",
        amount: "",
        method: "BANK_TRANSFER",
        status: "Completed",
        notes: "",
      });
      setDate(new Date());

      // Close dialog
      onOpenChange(false);

      // Show success message
      toast.success("Payment recorded successfully", {
        description: `Payment of $${formData.amount} has been recorded for invoice ${formData.invoiceId}`,
      });

    } catch (error) {
      console.error("Error recording payment:", error);
      toast.error("Failed to record payment", {
        description: error instanceof Error ? error.message : "Please try again later",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Record New Payment</DialogTitle>
          <DialogDescription>
            Enter the payment details below to record a new payment.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="invoiceId" className="text-right">
                Invoice ID
              </Label>
              <Input
                id="invoiceId"
                name="invoiceId"
                value={formData.invoiceId}
                onChange={handleChange}
                className="col-span-3"
                required
                readOnly={!!invoiceData}
                disabled={!!invoiceData}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="client" className="text-right">
                Client
              </Label>
              <Input
                id="client"
                name="client"
                value={formData.client}
                onChange={handleChange}
                className="col-span-3"
                required
                readOnly={!!invoiceData}
                disabled={!!invoiceData}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="amount" className="text-right">
                Amount
              </Label>
              <Input
                id="amount"
                name="amount"
                value={formData.amount}
                onChange={handleChange}
                placeholder="$0.00"
                className="col-span-3"
                required
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className="col-span-3 justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Method</Label>
              <Select
                value={formData.method}
                onValueChange={(value) => handleSelectChange("method", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md">
                  <SelectItem value="BANK_TRANSFER" className="cursor-pointer hover:bg-gray-100">Bank Transfer</SelectItem>
                  <SelectItem value="CREDIT_CARD" className="cursor-pointer hover:bg-gray-100">Credit Card</SelectItem>
                  <SelectItem value="DEBIT_CARD" className="cursor-pointer hover:bg-gray-100">Debit Card</SelectItem>
                  <SelectItem value="CHECK" className="cursor-pointer hover:bg-gray-100">Check</SelectItem>
                  <SelectItem value="CASH" className="cursor-pointer hover:bg-gray-100">Cash</SelectItem>
                  <SelectItem value="UPI" className="cursor-pointer hover:bg-gray-100">UPI</SelectItem>
                  <SelectItem value="NEFT" className="cursor-pointer hover:bg-gray-100">NEFT</SelectItem>
                  <SelectItem value="RTGS" className="cursor-pointer hover:bg-gray-100">RTGS</SelectItem>
                  <SelectItem value="IMPS" className="cursor-pointer hover:bg-gray-100">IMPS</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange("status", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select payment status" />
                </SelectTrigger>
                <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md">
                  <SelectItem value="Completed" className="cursor-pointer hover:bg-gray-100">Completed</SelectItem>
                  <SelectItem value="Pending" className="cursor-pointer hover:bg-gray-100">Pending</SelectItem>
                  <SelectItem value="Failed" className="cursor-pointer hover:bg-gray-100">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                Reference Number
              </Label>
              <Input
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                className="col-span-3"
                placeholder="Transaction reference or check number"
              />
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Recording...
                </>
              ) : (
                "Record Payment"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default RecordPaymentDialog;
