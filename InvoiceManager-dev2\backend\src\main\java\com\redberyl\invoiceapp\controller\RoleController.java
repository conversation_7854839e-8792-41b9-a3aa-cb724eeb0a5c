package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.auth.RoleDto;
import com.redberyl.invoiceapp.entity.auth.ERole;
import com.redberyl.invoiceapp.entity.auth.Role;
import com.redberyl.invoiceapp.repository.RoleRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/auth/roles")
@Tag(name = "Roles", description = "Role management API")
public class RoleController {

    @Autowired
    private RoleRepository roleRepository;

    @GetMapping
    @Operation(summary = "Get all roles", description = "Retrieve a list of all roles")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<List<RoleDto>> getAllRoles() {
        List<RoleDto> roles = roleRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        return new ResponseEntity<>(roles, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get role by ID", description = "Retrieve a role by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<RoleDto> getRoleById(@PathVariable Long id) {
        return roleRepository.findById(id)
                .map(role -> new ResponseEntity<>(convertToDto(role), HttpStatus.OK))
                .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @PostMapping
    @Operation(summary = "Create role", description = "Create a new role")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<RoleDto> createRole(@Valid @RequestBody RoleDto roleDto) {
        Role role = new Role();
        role.setName(ERole.valueOf(roleDto.getName()));
        Role savedRole = roleRepository.save(role);
        return new ResponseEntity<>(convertToDto(savedRole), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update role", description = "Update an existing role")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<RoleDto> updateRole(@PathVariable Long id, @Valid @RequestBody RoleDto roleDto) {
        return roleRepository.findById(id)
                .map(role -> {
                    role.setName(ERole.valueOf(roleDto.getName()));
                    Role updatedRole = roleRepository.save(role);
                    return new ResponseEntity<>(convertToDto(updatedRole), HttpStatus.OK);
                })
                .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete role", description = "Delete a role by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteRole(@PathVariable Long id) {
        return roleRepository.findById(id)
                .map(role -> {
                    roleRepository.delete(role);
                    return new ResponseEntity<Void>(HttpStatus.NO_CONTENT);
                })
                .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    private RoleDto convertToDto(Role role) {
        RoleDto dto = new RoleDto();
        dto.setId(role.getId());
        dto.setName(role.getName().name());
        dto.setDescription(getDescriptionForRole(role.getName()));
        return dto;
    }

    private String getDescriptionForRole(ERole role) {
        switch (role) {
            case ROLE_ADMIN:
                return "Administrator with full access";
            case ROLE_MODERATOR:
                return "Moderator with limited administrative access";
            case ROLE_USER:
                return "Regular user with basic access";
            default:
                return role.name();
        }
    }
}
