package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.PaymentDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.Payment;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.PaymentRepository;
import com.redberyl.invoiceapp.service.PaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PaymentServiceImpl implements PaymentService {

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Override
    public List<PaymentDto> getAllPayments() {
        List<Payment> payments = paymentRepository.findAll();
        if (payments.isEmpty()) {
            throw new NoContentException("No payments found");
        }
        return payments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public PaymentDto getPaymentById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Payment ID cannot be null");
        }

        Payment payment = paymentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Payment not found with id: " + id));
        return convertToDto(payment);
    }

    @Override
    public List<PaymentDto> getPaymentsByInvoiceId(Long invoiceId) {
        if (invoiceId == null) {
            throw new NullConstraintViolationException("invoiceId", "Invoice ID cannot be null");
        }

        // Check if invoice exists
        if (!invoiceRepository.existsById(invoiceId)) {
            throw new ResourceNotFoundException("Invoice not found with id: " + invoiceId);
        }

        List<Payment> payments = paymentRepository.findByInvoiceId(invoiceId);
        if (payments.isEmpty()) {
            throw new NoContentException("No payments found for invoice with id: " + invoiceId);
        }

        return payments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<PaymentDto> getPaymentsByDateRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null) {
            throw new NullConstraintViolationException("startDate", "Start date cannot be null");
        }

        if (endDate == null) {
            throw new NullConstraintViolationException("endDate", "End date cannot be null");
        }

        if (startDate.isAfter(endDate)) {
            throw new CustomException("Start date cannot be after end date", null);
        }

        List<Payment> payments = paymentRepository.findByReceivedOnBetween(startDate, endDate);
        if (payments.isEmpty()) {
            throw new NoContentException("No payments found between " + startDate + " and " + endDate);
        }

        return payments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<PaymentDto> getPaymentsByPaymentMode(String paymentMode) {
        if (!StringUtils.hasText(paymentMode)) {
            throw new NullConstraintViolationException("paymentMode", "Payment mode cannot be empty");
        }

        List<Payment> payments = paymentRepository.findByPaymentMode(paymentMode);
        if (payments.isEmpty()) {
            throw new NoContentException("No payments found with payment mode: " + paymentMode);
        }

        return payments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validatePaymentDto(PaymentDto paymentDto) {
        if (paymentDto == null) {
            throw new NullConstraintViolationException("paymentDto", "Payment data cannot be null");
        }

        if (paymentDto.getInvoiceId() == null) {
            throw new NullConstraintViolationException("invoiceId", "Invoice ID cannot be null");
        }

        if (!invoiceRepository.existsById(paymentDto.getInvoiceId())) {
            throw new ForeignKeyViolationException("invoiceId",
                    "Invoice not found with id: " + paymentDto.getInvoiceId());
        }

        if (paymentDto.getAmountReceived() == null) {
            throw new NullConstraintViolationException("amountReceived", "Amount received cannot be null");
        }

        if (paymentDto.getReceivedOn() == null) {
            throw new NullConstraintViolationException("receivedOn", "Received date cannot be null");
        }

        if (!StringUtils.hasText(paymentDto.getPaymentMode())) {
            throw new NullConstraintViolationException("paymentMode", "Payment mode cannot be empty");
        }
    }

    @Override
    @Transactional
    public PaymentDto createPayment(PaymentDto paymentDto) {
        validatePaymentDto(paymentDto);

        try {
            Payment payment = convertToEntity(paymentDto);
            Payment savedPayment = paymentRepository.save(payment);
            return convertToDto(savedPayment);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating payment: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating payment", e);
        }
    }

    @Override
    @Transactional
    public PaymentDto updatePayment(Long id, PaymentDto paymentDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Payment ID cannot be null");
        }

        if (paymentDto == null) {
            throw new NullConstraintViolationException("paymentDto", "Payment data cannot be null");
        }

        Payment existingPayment = paymentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Payment not found with id: " + id));

        try {
            if (paymentDto.getInvoiceId() != null) {
                if (!invoiceRepository.existsById(paymentDto.getInvoiceId())) {
                    throw new ForeignKeyViolationException("invoiceId",
                            "Invoice not found with id: " + paymentDto.getInvoiceId());
                }

                Invoice invoice = invoiceRepository.findById(paymentDto.getInvoiceId()).get();
                existingPayment.setInvoice(invoice);
            }

            if (paymentDto.getAmountReceived() != null) {
                existingPayment.setAmountReceived(paymentDto.getAmountReceived());
            }

            if (paymentDto.getReceivedOn() != null) {
                existingPayment.setReceivedOn(paymentDto.getReceivedOn());
            }

            if (StringUtils.hasText(paymentDto.getPaymentMode())) {
                existingPayment.setPaymentMode(paymentDto.getPaymentMode());
            }

            if (StringUtils.hasText(paymentDto.getReferenceNumber())) {
                existingPayment.setReferenceNumber(paymentDto.getReferenceNumber());
            }

            Payment updatedPayment = paymentRepository.save(existingPayment);
            return convertToDto(updatedPayment);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating payment: " + e.getMessage(), e);
            }
        } catch (ResourceNotFoundException | NullConstraintViolationException | ForeignKeyViolationException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomException("Error updating payment", e);
        }
    }

    @Override
    @Transactional
    public void deletePayment(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Payment ID cannot be null");
        }

        if (!paymentRepository.existsById(id)) {
            throw new ResourceNotFoundException("Payment not found with id: " + id);
        }

        try {
            paymentRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete payment because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting payment: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting payment", e);
        }
    }

    private PaymentDto convertToDto(Payment payment) {
        // Start building the DTO with common fields
        PaymentDto.PaymentDtoBuilder builder = PaymentDto.builder()
                .id(payment.getId())
                .amountReceived(payment.getAmountReceived())
                .receivedOn(payment.getReceivedOn())
                .paymentMode(payment.getPaymentMode())
                .referenceNumber(payment.getReferenceNumber());

        // Add invoice and client information if available
        if (payment.getInvoice() != null) {
            builder.invoiceId(payment.getInvoice().getId())
                   .invoiceNumber(payment.getInvoice().getInvoiceNumber());

            // Add client name if available
            if (payment.getInvoice().getClient() != null) {
                builder.clientName(payment.getInvoice().getClient().getName());
            }
        }

        // Build the DTO
        PaymentDto dto = builder.build();

        // Set the audit fields
        dto.setCreatedAt(payment.getCreatedAt());
        dto.setUpdatedAt(payment.getModifiedAt());

        return dto;
    }

    private Payment convertToEntity(PaymentDto dto) {
        Payment payment = new Payment();
        payment.setId(dto.getId());

        if (dto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(dto.getInvoiceId())
                    .orElseThrow(
                            () -> new ResourceNotFoundException("Invoice not found with id: " + dto.getInvoiceId()));
            payment.setInvoice(invoice);
        }

        payment.setAmountReceived(dto.getAmountReceived());
        payment.setReceivedOn(dto.getReceivedOn());
        payment.setPaymentMode(dto.getPaymentMode());
        payment.setReferenceNumber(dto.getReferenceNumber());

        return payment;
    }
}
