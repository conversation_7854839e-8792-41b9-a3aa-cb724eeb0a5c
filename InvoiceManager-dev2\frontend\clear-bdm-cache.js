// BDM Cache Clearing Script
// Run this in browser console (F12 -> Console) to clear all BDM caches

console.log('🧹 Starting comprehensive BDM cache clearing...');

// Clear localStorage
const localStorageKeys = Object.keys(localStorage);
localStorageKeys.forEach(key => {
  if (key.toLowerCase().includes('bdm')) {
    localStorage.removeItem(key);
    console.log(`Removed localStorage key: ${key}`);
  }
});

// Clear common cache keys
const commonCacheKeys = [
  'bdms_cache',
  'bdm_cache',
  'entityData_bdms',
  'entityData_bdm',
  'useEntityData_bdms',
  'api_cache_bdms',
  'api_cache_bdm'
];

commonCacheKeys.forEach(key => {
  localStorage.removeItem(key);
  sessionStorage.removeItem(key);
  console.log(`Cleared cache key: ${key}`);
});

// Clear sessionStorage
try {
  const sessionStorageKeys = Object.keys(sessionStorage);
  sessionStorageKeys.forEach(key => {
    if (key.toLowerCase().includes('bdm')) {
      sessionStorage.removeItem(key);
      console.log(`Removed sessionStorage key: ${key}`);
    }
  });
} catch (e) {
  console.warn('Could not access sessionStorage:', e);
}

// Clear any potential IndexedDB caches (if used)
try {
  if ('indexedDB' in window) {
    // This is a basic attempt - actual implementation would depend on your IndexedDB usage
    console.log('IndexedDB available - you may need to clear it manually if used');
  }
} catch (e) {
  console.warn('Could not access IndexedDB:', e);
}

console.log('✅ BDM cache clearing completed!');
console.log('🔄 Reloading page to fetch fresh data...');

// Force reload the page
setTimeout(() => {
  window.location.reload(true);
}, 1000);
