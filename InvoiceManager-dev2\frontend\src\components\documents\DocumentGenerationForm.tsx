import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon, Download, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { clientService, type Client } from "@/services/clientService";

// Define the form schema with Zod
const formSchema = z.object({
  templateId: z.string({
    required_error: "Please select a template",
  }),
  clientId: z.string({
    required_error: "Please select a client",
  }),
  variables: z.record(z.string()),
});

type FormValues = z.infer<typeof formSchema>;

// Mock data - would be fetched from API in real application
const templatesData = [
  {
    id: "1",
    name: "Invoice Template",
    type: "PDF",
    variables: ["client_name", "client_email", "client_phone", "client_address", "invoice_number", "amount", "date", "due_date"],
  },
  {
    id: "2",
    name: "Client Agreement",
    type: "DOCX",
    variables: ["client_name", "client_email", "client_phone", "client_address", "project_name", "start_date", "fee_structure"],
  },
  {
    id: "3",
    name: "Project Proposal",
    type: "PDF",
    variables: ["client_name", "client_email", "client_phone", "client_address", "project_scope", "timeline", "budget", "deliverables"],
  },
  {
    id: "4",
    name: "Employment Contract",
    type: "DOCX",
    variables: ["employee_name", "position", "salary", "start_date", "benefits"],
  },
  {
    id: "5",
    name: "Non-Disclosure Agreement",
    type: "PDF",
    variables: ["client_name", "client_email", "client_address", "effective_date", "confidential_information"],
  },
];

// Clients will be fetched from API

interface DocumentGenerationFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGenerate?: (data: any) => void;
}

const DocumentGenerationForm: React.FC<DocumentGenerationFormProps> = ({
  open,
  onOpenChange,
  onGenerate,
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoadingClients, setIsLoadingClients] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      templateId: "",
      clientId: "",
      variables: {},
    },
  });

  // Fetch clients from API
  const fetchClients = async (retryCount = 0) => {
    setIsLoadingClients(true);
    try {
      const clientsData = await clientService.getAllClients();
      setClients(clientsData);
      console.log("Successfully loaded clients:", clientsData.length);
    } catch (error) {
      console.error("Error fetching clients:", error);

      // Retry once if it's the first attempt
      if (retryCount === 0) {
        console.log("Retrying client fetch...");
        setTimeout(() => fetchClients(1), 1000);
        return;
      }

      toast.error("Failed to load clients", {
        description: "Please try refreshing the page or contact support",
        action: {
          label: "Retry",
          onClick: () => fetchClients(0),
        },
      });
    } finally {
      setIsLoadingClients(false);
    }
  };

  // Reset form when dialog opens/closes and fetch clients
  useEffect(() => {
    if (open) {
      form.reset();
      setSelectedTemplate(null);
      setSelectedClient(null);
      fetchClients();
    }
  }, [open, form]);

  // Handle client selection
  const handleClientChange = (clientId: string) => {
    const client = clients.find((c) => c.id.toString() === clientId);
    setSelectedClient(client || null);

    // Auto-populate client-related variables if template is selected
    if (selectedTemplate && client) {
      const currentVariables = form.getValues("variables") || {};
      const updatedVariables = { ...currentVariables };

      // Auto-populate client name
      if (selectedTemplate.variables.includes("client_name")) {
        updatedVariables["client_name"] = client.name;
      }

      // Auto-populate other client fields if they exist in template
      if (selectedTemplate.variables.includes("client_email") && client.email) {
        updatedVariables["client_email"] = client.email;
      }
      if (selectedTemplate.variables.includes("client_phone") && client.phone) {
        updatedVariables["client_phone"] = client.phone;
      }
      if (selectedTemplate.variables.includes("client_address") && client.billingAddress) {
        updatedVariables["client_address"] = client.billingAddress;
      }

      form.setValue("variables", updatedVariables);
    }
  };

  // Update variable fields when template changes
  const handleTemplateChange = (templateId: string) => {
    const template = templatesData.find((t) => t.id === templateId);
    setSelectedTemplate(template);

    // Reset variables
    const initialVariables: Record<string, string> = {};
    if (template) {
      template.variables.forEach((variable) => {
        initialVariables[variable] = "";
      });

      // Auto-populate client data if client is already selected
      if (selectedClient) {
        if (template.variables.includes("client_name")) {
          initialVariables["client_name"] = selectedClient.name;
        }
        if (template.variables.includes("client_email") && selectedClient.email) {
          initialVariables["client_email"] = selectedClient.email;
        }
        if (template.variables.includes("client_phone") && selectedClient.phone) {
          initialVariables["client_phone"] = selectedClient.phone;
        }
        if (template.variables.includes("client_address") && selectedClient.billingAddress) {
          initialVariables["client_address"] = selectedClient.billingAddress;
        }
      }
    }

    form.setValue("variables", initialVariables);
  };

  const onSubmit = async (data: FormValues) => {
    setIsGenerating(true);

    try {
      // Validate that we have the required data
      if (!data.templateId || !data.clientId) {
        throw new Error("Please select both a template and a client");
      }

      // In a real app, you would send this data to your API
      console.log("Generating document with data:", data);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      const template = templatesData.find((t) => t.id === data.templateId);
      const client = clients.find((c) => c.id.toString() === data.clientId);

      if (!template) {
        throw new Error("Selected template not found");
      }
      if (!client) {
        throw new Error("Selected client not found");
      }

      // Generate a filename based on template and client
      const timestamp = format(new Date(), "yyyy-MM-dd");
      const filename = `${template.name.replace(/\s+/g, "_")}_${client.name.replace(/\s+/g, "_")}_${timestamp}.${template.type.toLowerCase()}`;

      const generatedDocument = {
        id: `gen-${Date.now()}`,
        name: filename,
        template: template.name,
        client: client.name,
        generatedDate: new Date().toISOString(),
        createdBy: "Current User",
        variables: data.variables,
      };

      if (onGenerate) {
        onGenerate(generatedDocument);
      }

      toast.success("Document generated successfully", {
        description: filename,
      });

      onOpenChange(false);
    } catch (error) {
      console.error("Error generating document:", error);
      toast.error("Failed to generate document", {
        description: "Please try again later",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Generate Document</DialogTitle>
          <DialogDescription>
            Fill in the details to generate a new document from a template.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="templateId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Template</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleTemplateChange(value);
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a template" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md">
                      {templatesData.map((template) => (
                        <SelectItem key={template.id} value={template.id} className="cursor-pointer hover:bg-gray-100">
                          {template.name} ({template.type})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="clientId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleClientChange(value);
                    }}
                    defaultValue={field.value}
                    disabled={isLoadingClients}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={
                          isLoadingClients ? "Loading clients..." : "Select a client"
                        } />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md">
                      {isLoadingClients ? (
                        <SelectItem value="loading" disabled className="flex items-center">
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading clients...
                        </SelectItem>
                      ) : clients.length === 0 ? (
                        <SelectItem value="no-clients" disabled>
                          No clients found
                        </SelectItem>
                      ) : (
                        clients.map((client) => (
                          <SelectItem
                            key={client.id}
                            value={client.id.toString()}
                            className="cursor-pointer hover:bg-gray-100"
                          >
                            {client.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedTemplate && (
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Template Variables</h3>
                <p className="text-xs text-gray-500">
                  Client information will be auto-populated when you select a client.
                </p>
                {selectedTemplate.variables.map((variable: string) => {
                  const isClientField = variable.startsWith('client_');
                  const isAutoPopulated = isClientField && selectedClient;

                  // Create better labels
                  const getFieldLabel = (fieldName: string) => {
                    const labelMap: Record<string, string> = {
                      'client_name': 'Client Name',
                      'client_email': 'Client Email',
                      'client_phone': 'Client Phone',
                      'client_address': 'Client Address',
                      'invoice_number': 'Invoice Number',
                      'amount': 'Amount',
                      'date': 'Date',
                      'due_date': 'Due Date',
                      'project_name': 'Project Name',
                      'start_date': 'Start Date',
                      'fee_structure': 'Fee Structure',
                      'project_scope': 'Project Scope',
                      'timeline': 'Timeline',
                      'budget': 'Budget',
                      'deliverables': 'Deliverables',
                      'employee_name': 'Employee Name',
                      'position': 'Position',
                      'salary': 'Salary',
                      'benefits': 'Benefits',
                      'effective_date': 'Effective Date',
                      'confidential_information': 'Confidential Information',
                    };
                    return labelMap[fieldName] || fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                  };

                  return (
                    <FormField
                      key={variable}
                      control={form.control}
                      name={`variables.${variable}`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className={isAutoPopulated ? "text-green-600" : ""}>
                            {getFieldLabel(variable)}
                            {isAutoPopulated && (
                              <span className="ml-1 text-xs text-green-500">(auto-filled)</span>
                            )}
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder={isClientField && !selectedClient ? "Select a client to auto-fill" : ""}
                              className={isAutoPopulated ? "border-green-200 bg-green-50" : ""}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  );
                })}
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isGenerating}>
                {isGenerating ? (
                  <>Generating...</>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Generate Document
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentGenerationForm;
