/**
 * Invoice Generation Service
 *
 * Service for generating invoices in various formats
 */

import { Invoice } from "@/types/invoice";

/**
 * Invoice Generation Service
 */
export const invoiceGenerationService = {
  /**
   * Test if the invoice generation API is available
   * @returns A promise that resolves to true if the API is available, false otherwise
   */
  testApiAvailability: async (): Promise<boolean> => {
    try {
      console.log('Testing API availability...');

      // Use Vite proxy endpoints
      const apiUrls = [
        '/api/invoice-generation/test',
        '/api/invoice-generation/test'
      ];

      // Try each endpoint until one works
      for (const apiUrl of apiUrls) {
        try {
          console.log('Attempting to connect to test endpoint:', apiUrl);

          const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
              'Accept': 'text/plain',
            },
          });

          if (response.ok) {
            const text = await response.text();
            console.log(`API test successful at ${apiUrl}: ${text}`);
            return true;
          }
        } catch (error) {
          console.error(`Error with test endpoint ${apiUrl}:`, error);
          // Continue to the next URL
        }
      }

      // If we get here, all endpoints failed
      console.error('All API test endpoints failed');
      return false;
    } catch (error) {
      console.error('Error testing API availability:', error);
      return false;
    }
  },
  /**
   * Generate a PDF invoice from an existing invoice
   * @param invoiceId The ID of the invoice to generate
   * @returns A URL to download the PDF
   */
  generatePdfInvoice: async (invoiceId: string | number): Promise<string> => {
    return `/api/invoice-generation/pdf/${invoiceId}`;
  },

  /**
   * Generate a PDF invoice preview from invoice data without saving it
   * @param invoiceData The invoice data to use for generation
   * @returns A blob URL to the generated PDF
   */
  generatePdfInvoicePreview: async (invoiceData: Invoice): Promise<string> => {
    try {
      console.log('Sending invoice data to API:', JSON.stringify(invoiceData, null, 2));

      // Use Vite proxy endpoint
      const apiUrls = [
        '/api/invoice-generation/pdf/preview'
      ];

      let lastError = null;

      // Try each endpoint until one works
      for (const apiUrl of apiUrls) {
        try {
          console.log('Attempting to connect to:', apiUrl);

          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(invoiceData),
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(`API error response from ${apiUrl}:`, errorText);
            lastError = new Error(`Failed to generate PDF preview: ${response.status} - ${errorText}`);
            continue; // Try the next URL
          }

          // If we get here, the request was successful
          const blob = await response.blob();
          return URL.createObjectURL(blob);
        } catch (error) {
          console.error(`Error with endpoint ${apiUrl}:`, error);
          lastError = error;
          // Continue to the next URL
        }
      }

      // If we get here, all endpoints failed
      throw lastError || new Error('All API endpoints failed');
    } catch (error) {
      console.error('Error generating PDF preview:', error);
      throw error;
    }
  },

  /**
   * Generate an HTML invoice from an existing invoice
   * @param invoiceId The ID of the invoice to generate
   * @returns The HTML content as a string
   */
  generateHtmlInvoice: async (invoiceId: string | number): Promise<string> => {
    try {
      const response = await fetch(`/api/invoice-generation/html/${invoiceId}`, {
        method: 'GET',
        headers: {
          'Accept': 'text/html',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to generate HTML invoice: ${response.status}`);
      }

      return await response.text();
    } catch (error) {
      console.error('Error generating HTML invoice:', error);
      throw error;
    }
  },

  /**
   * Generate an HTML invoice preview from invoice data without saving it
   * @param invoiceData The invoice data to use for generation
   * @returns The HTML content as a string
   */
  generateHtmlInvoicePreview: async (invoiceData: Invoice): Promise<string> => {
    try {
      console.log('Sending invoice data to API for HTML preview:', JSON.stringify(invoiceData, null, 2));

      // Use Vite proxy endpoint
      const apiUrls = [
        '/api/invoice-generation/html/preview'
      ];

      let lastError = null;

      // Try each endpoint until one works
      for (const apiUrl of apiUrls) {
        try {
          console.log('Attempting to connect to HTML preview endpoint:', apiUrl);

          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'text/html',
            },
            body: JSON.stringify(invoiceData),
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(`API error response from ${apiUrl}:`, errorText);
            lastError = new Error(`Failed to generate HTML preview: ${response.status} - ${errorText}`);
            continue; // Try the next URL
          }

          // If we get here, the request was successful
          return await response.text();
        } catch (error) {
          console.error(`Error with HTML endpoint ${apiUrl}:`, error);
          lastError = error;
          // Continue to the next URL
        }
      }

      // If we get here, all endpoints failed
      throw lastError || new Error('All API endpoints failed for HTML preview');
    } catch (error) {
      console.error('Error generating HTML preview:', error);
      throw error;
    }
  },

  /**
   * Open a PDF invoice in a new tab
   * @param invoiceId The ID of the invoice to open
   */
  openPdfInvoice: (invoiceId: string | number): void => {
    const url = `/api/invoice-generation/pdf/${invoiceId}`;
    window.open(url, '_blank');
  },

  /**
   * Open a PDF invoice preview in a new tab
   * @param invoiceData The invoice data to use for generation
   */
  openPdfInvoicePreview: async (invoiceData: Invoice): Promise<void> => {
    try {
      console.log('Generating PDF preview for invoice:', invoiceData.invoiceNumber);

      // Add a loading message
      const loadingMessage = document.createElement('div');
      loadingMessage.style.position = 'fixed';
      loadingMessage.style.top = '50%';
      loadingMessage.style.left = '50%';
      loadingMessage.style.transform = 'translate(-50%, -50%)';
      loadingMessage.style.padding = '20px';
      loadingMessage.style.background = 'rgba(0, 0, 0, 0.7)';
      loadingMessage.style.color = 'white';
      loadingMessage.style.borderRadius = '5px';
      loadingMessage.style.zIndex = '9999';
      loadingMessage.textContent = 'Checking API availability...';
      document.body.appendChild(loadingMessage);

      try {
        // First check if the API is available
        const isApiAvailable = await invoiceGenerationService.testApiAvailability();

        if (!isApiAvailable) {
          loadingMessage.textContent = 'API not available. Please check server status.';
          await new Promise(resolve => setTimeout(resolve, 2000)); // Show message for 2 seconds
          throw new Error('API not available. Please check that the backend server is running and accessible through the proxy.');
        }

        // If API is available, proceed with generating the invoice
        loadingMessage.textContent = 'Generating invoice preview...';

        // Try to generate the PDF
        const url = await invoiceGenerationService.generatePdfInvoicePreview(invoiceData);
        console.log('PDF preview generated successfully, opening in new tab');
        window.open(url, '_blank');
      } finally {
        // Remove the loading message
        document.body.removeChild(loadingMessage);
      }
    } catch (error) {
      console.error('Error opening PDF preview:', error);

      // Show a more detailed error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const fullMessage = `Failed to generate invoice: ${errorMessage}\n\nPlease check that the backend server is running and accessible through the proxy.`;

      alert(fullMessage);
      throw error;
    }
  }
};
