package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceTemplateConfigDto;
import com.redberyl.invoiceapp.service.InvoiceTemplateConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controller for managing invoice template configurations
 */
@RestController
@RequestMapping("/api/invoice-template-config")
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"})
@Tag(name = "Invoice Template Configuration", description = "API for managing dynamic invoice template configurations")
public class InvoiceTemplateConfigController {

    @Autowired
    private InvoiceTemplateConfigService configService;

    /**
     * Get all configurations
     */
    @GetMapping
    @Operation(summary = "Get all configurations", description = "Retrieve all active invoice template configurations")
    public ResponseEntity<List<InvoiceTemplateConfigDto>> getAllConfigurations() {
        List<InvoiceTemplateConfigDto> configurations = configService.getAllConfigurations();
        return ResponseEntity.ok(configurations);
    }

    /**
     * Get configuration by ID
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get configuration by ID", description = "Retrieve a specific configuration by its ID")
    public ResponseEntity<InvoiceTemplateConfigDto> getConfigurationById(@PathVariable Long id) {
        InvoiceTemplateConfigDto configuration = configService.getConfigurationById(id);
        return ResponseEntity.ok(configuration);
    }

    /**
     * Get configuration by key
     */
    @GetMapping("/key/{configKey}")
    @Operation(summary = "Get configuration by key", description = "Retrieve a specific configuration by its key")
    public ResponseEntity<InvoiceTemplateConfigDto> getConfigurationByKey(@PathVariable String configKey) {
        InvoiceTemplateConfigDto configuration = configService.getConfigurationByKey(configKey);
        return ResponseEntity.ok(configuration);
    }

    /**
     * Get configurations by category
     */
    @GetMapping("/category/{category}")
    @Operation(summary = "Get configurations by category", description = "Retrieve all configurations for a specific category")
    public ResponseEntity<List<InvoiceTemplateConfigDto>> getConfigurationsByCategory(@PathVariable String category) {
        List<InvoiceTemplateConfigDto> configurations = configService.getConfigurationsByCategory(category);
        return ResponseEntity.ok(configurations);
    }

    /**
     * Get all configuration values as a map
     */
    @GetMapping("/values")
    @Operation(summary = "Get all configuration values", description = "Retrieve all configuration values as key-value pairs")
    public ResponseEntity<Map<String, String>> getAllConfigValues() {
        Map<String, String> configValues = configService.getAllConfigValues();
        return ResponseEntity.ok(configValues);
    }

    /**
     * Get configuration values by category as a map
     */
    @GetMapping("/values/category/{category}")
    @Operation(summary = "Get configuration values by category", description = "Retrieve configuration values for a specific category as key-value pairs")
    public ResponseEntity<Map<String, String>> getConfigValuesByCategory(@PathVariable String category) {
        Map<String, String> configValues = configService.getConfigValuesByCategory(category);
        return ResponseEntity.ok(configValues);
    }

    /**
     * Create new configuration
     */
    @PostMapping
    @Operation(summary = "Create configuration", description = "Create a new invoice template configuration")
    public ResponseEntity<InvoiceTemplateConfigDto> createConfiguration(@Valid @RequestBody InvoiceTemplateConfigDto configDto) {
        InvoiceTemplateConfigDto createdConfig = configService.createConfiguration(configDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdConfig);
    }

    /**
     * Update existing configuration
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update configuration", description = "Update an existing invoice template configuration")
    public ResponseEntity<InvoiceTemplateConfigDto> updateConfiguration(
            @PathVariable Long id,
            @Valid @RequestBody InvoiceTemplateConfigDto configDto) {
        InvoiceTemplateConfigDto updatedConfig = configService.updateConfiguration(id, configDto);
        return ResponseEntity.ok(updatedConfig);
    }

    /**
     * Delete configuration
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete configuration", description = "Delete an invoice template configuration")
    public ResponseEntity<Void> deleteConfiguration(@PathVariable Long id) {
        configService.deleteConfiguration(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Bulk update configurations
     */
    @PutMapping("/bulk")
    @Operation(summary = "Bulk update configurations", description = "Update multiple configurations at once")
    public ResponseEntity<List<InvoiceTemplateConfigDto>> bulkUpdateConfigurations(
            @Valid @RequestBody List<InvoiceTemplateConfigDto> configDtos) {
        List<InvoiceTemplateConfigDto> updatedConfigs = configService.bulkUpdateConfigurations(configDtos);
        return ResponseEntity.ok(updatedConfigs);
    }

    /**
     * Initialize default configurations
     */
    @PostMapping("/initialize")
    @Operation(summary = "Initialize default configurations", description = "Initialize default invoice template configurations")
    public ResponseEntity<String> initializeDefaultConfigurations() {
        configService.initializeDefaultConfigurations();
        return ResponseEntity.ok("Default configurations initialized successfully");
    }
}
