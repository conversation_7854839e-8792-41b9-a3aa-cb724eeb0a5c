package com.redberyl.invoiceapp.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class SwaggerUiConfig implements WebMvcConfigurer {

    @Value("${springdoc.swagger-ui.path:/swagger-ui}")
    private String swaggerPath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Register custom static resources for Swagger UI
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/static/swagger-ui/");
    }

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Redberyl Invoice CRM API")
                        .description("Complete Invoice CRM and Document Management System API")
                        .version("1.0.0")
                        .termsOfService("https://redberyl.com/terms")
                        .contact(new Contact()
                                .name("Redberyl Support")
                                .email("<EMAIL>")
                                .url("https://redberyl.com/contact"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0.html")));
    }
}
