import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown } from "lucide-react";

interface CandidateStatusButtonProps {
  currentStatus: string;
  onStatusChange: (newStatus: string) => void;
}

const CandidateStatusButton: React.FC<CandidateStatusButtonProps> = ({
  currentStatus,
  onStatusChange,
}) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "inactive":
        return "bg-red-100 text-red-800 border-red-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 p-0 flex items-center gap-1 hover:bg-transparent w-full justify-start">
          <Badge className={`${getStatusColor(currentStatus)} px-2 py-1 text-xs sm:text-sm`} variant="outline">
            {currentStatus}
          </Badge>
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[120px]">
        <DropdownMenuItem
          onClick={() => onStatusChange("Active")}
          className="cursor-pointer"
        >
          <Badge className="bg-green-100 text-green-800 border-green-200 w-full justify-center" variant="outline">
            Active
          </Badge>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onStatusChange("Inactive")}
          className="cursor-pointer"
        >
          <Badge className="bg-red-100 text-red-800 border-red-200 w-full justify-center" variant="outline">
            Inactive
          </Badge>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onStatusChange("Pending")}
          className="cursor-pointer"
        >
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 w-full justify-center" variant="outline">
            Pending
          </Badge>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CandidateStatusButton;
