package com.redberyl.invoiceapp.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Email;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class ClientDto extends BaseDto {
    private Long id;

    @NotBlank(message = "Client name is required")
    private String name;

    @Email(message = "Invalid email format")
    private String email;

    private String phone;
    private String contactPerson;
    private String website;
    private String industry;
    private Long bdmId;
    private BigDecimal commissionPercentage;
    private String billingAddress;
    private String shippingAddress;
    private String gstNumber;
    private String panNumber;
    private String cinNumber;
    private String notes;

    // Remove circular references to avoid compilation issues
    // @Builder.Default
    // private Set<ProjectDto> projects = new HashSet<>();
    //
    // @Builder.Default
    // private Set<InvoiceDto> invoices = new HashSet<>();
}
