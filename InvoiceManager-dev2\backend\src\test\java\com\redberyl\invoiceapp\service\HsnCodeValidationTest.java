package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.HsnCodeDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

public class HsnCodeValidationTest {

    private HsnCodeDto validHsnCodeDto;
    private HsnCodeDto invalidHsnCodeDto;

    @BeforeEach
    void setUp() {
        validHsnCodeDto = HsnCodeDto.builder()
                .code("998313")
                .description("IT consulting services")
                .gstRate(new BigDecimal("18.0"))
                .build();

        invalidHsnCodeDto = HsnCodeDto.builder()
                .code("9983") // Invalid: only 4 digits
                .description("IT Services")
                .gstRate(new BigDecimal("18.0"))
                .build();
    }

    @Test
    void testValidHsnCodeCreation() {
        // This should not throw any exception
        assertDoesNotThrow(() -> {
            // Test that a valid HSN code passes format validation
            assertTrue(validHsnCodeDto.getCode().matches("^[0-9]{6}$"));
        });
    }

    @Test
    void testInvalidHsnCodeValidation() {
        // Test various invalid HSN code formats
        String[] invalidCodes = {
            "9983",      // Too short (4 digits)
            "99831",     // Too short (5 digits)
            "9983134",   // Too long (7 digits)
            "99831a",    // Contains letter
            "998-31",    // Contains special character
            "",          // Empty
            "99 831",    // Contains space
            "ABCDEF"     // All letters
        };

        for (String invalidCode : invalidCodes) {
            assertFalse(invalidCode.matches("^[0-9]{6}$"), 
                "HSN code '" + invalidCode + "' should be invalid");
        }
    }

    @Test
    void testValidHsnCodeFormats() {
        // Test various valid HSN code formats
        String[] validCodes = {
            "998313",    // Standard IT services code
            "000001",    // With leading zeros
            "123456",    // Random 6 digits
            "999999"     // All 9s
        };

        for (String validCode : validCodes) {
            assertTrue(validCode.matches("^[0-9]{6}$"), 
                "HSN code '" + validCode + "' should be valid");
        }
    }

    @Test
    void testHsnCodeDtoValidation() {
        // Test that the DTO validation annotations work
        assertTrue(validHsnCodeDto.getCode().length() == 6);
        assertTrue(validHsnCodeDto.getCode().matches("^[0-9]{6}$"));
        
        assertFalse(invalidHsnCodeDto.getCode().length() == 6);
        assertFalse(invalidHsnCodeDto.getCode().matches("^[0-9]{6}$"));
    }
}
