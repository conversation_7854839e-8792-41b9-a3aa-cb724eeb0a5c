package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "invoice_types")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InvoiceType extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "invoice_type", nullable = false, unique = true)
    private String invoiceType;

    @Column(name = "type_desc")
    private String typeDesc;

    @OneToMany(mappedBy = "invoiceType")
    private Set<Invoice> invoices = new HashSet<>();
}
