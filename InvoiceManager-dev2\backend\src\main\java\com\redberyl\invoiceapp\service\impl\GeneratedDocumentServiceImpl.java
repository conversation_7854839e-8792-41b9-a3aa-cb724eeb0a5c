package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.GeneratedDocumentDto;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Deal;
import com.redberyl.invoiceapp.entity.DocumentTemplate;
import com.redberyl.invoiceapp.entity.DocumentTemplateVersion;
import com.redberyl.invoiceapp.entity.GeneratedDocument;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.DealRepository;
import com.redberyl.invoiceapp.repository.DocumentTemplateRepository;
import com.redberyl.invoiceapp.repository.DocumentTemplateVersionRepository;
import com.redberyl.invoiceapp.repository.GeneratedDocumentRepository;
import com.redberyl.invoiceapp.service.GeneratedDocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class GeneratedDocumentServiceImpl implements GeneratedDocumentService {

    @Autowired
    private GeneratedDocumentRepository generatedDocumentRepository;

    @Autowired
    private DocumentTemplateRepository documentTemplateRepository;

    @Autowired
    private DocumentTemplateVersionRepository documentTemplateVersionRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private DealRepository dealRepository;

    @Override
    public List<GeneratedDocumentDto> getAllGeneratedDocuments() {
        List<GeneratedDocument> generatedDocuments = generatedDocumentRepository.findAll();
        if (generatedDocuments.isEmpty()) {
            throw new NoContentException("No generated documents found");
        }
        return generatedDocuments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public GeneratedDocumentDto getGeneratedDocumentById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Generated document ID cannot be null");
        }

        GeneratedDocument generatedDocument = generatedDocumentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Generated Document not found with id: " + id));
        return convertToDto(generatedDocument);
    }

    @Override
    public List<GeneratedDocumentDto> getGeneratedDocumentsByTemplateId(Long templateId) {
        if (templateId == null) {
            throw new NullConstraintViolationException("templateId", "Template ID cannot be null");
        }

        // Check if template exists
        if (!documentTemplateRepository.existsById(templateId)) {
            throw new ResourceNotFoundException("Document Template not found with id: " + templateId);
        }

        List<GeneratedDocument> generatedDocuments = generatedDocumentRepository.findByTemplateId(templateId);
        if (generatedDocuments.isEmpty()) {
            throw new NoContentException("No generated documents found for template with id: " + templateId);
        }

        return generatedDocuments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<GeneratedDocumentDto> getGeneratedDocumentsByClientId(Long clientId) {
        if (clientId == null) {
            throw new NullConstraintViolationException("clientId", "Client ID cannot be null");
        }

        // Check if client exists
        if (!clientRepository.existsById(clientId)) {
            throw new ResourceNotFoundException("Client not found with id: " + clientId);
        }

        List<GeneratedDocument> generatedDocuments = generatedDocumentRepository.findByClientId(clientId);
        if (generatedDocuments.isEmpty()) {
            throw new NoContentException("No generated documents found for client with id: " + clientId);
        }

        return generatedDocuments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<GeneratedDocumentDto> getGeneratedDocumentsByDealId(Long dealId) {
        if (dealId == null) {
            throw new NullConstraintViolationException("dealId", "Deal ID cannot be null");
        }

        // Check if deal exists
        if (!dealRepository.existsById(dealId)) {
            throw new ResourceNotFoundException("Deal not found with id: " + dealId);
        }

        List<GeneratedDocument> generatedDocuments = generatedDocumentRepository.findByDealId(dealId);
        if (generatedDocuments.isEmpty()) {
            throw new NoContentException("No generated documents found for deal with id: " + dealId);
        }

        return generatedDocuments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<GeneratedDocumentDto> getGeneratedDocumentsByStatus(String status) {
        if (!StringUtils.hasText(status)) {
            throw new NullConstraintViolationException("status", "Status cannot be empty");
        }

        List<GeneratedDocument> generatedDocuments = generatedDocumentRepository.findByStatus(status);
        if (generatedDocuments.isEmpty()) {
            throw new NoContentException("No generated documents found with status: " + status);
        }

        return generatedDocuments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateGeneratedDocumentDto(GeneratedDocumentDto generatedDocumentDto) {
        if (generatedDocumentDto == null) {
            throw new NullConstraintViolationException("generatedDocumentDto",
                    "Generated document data cannot be null");
        }

        // Validate template ID if provided
        if (generatedDocumentDto.getTemplateId() != null &&
                !documentTemplateRepository.existsById(generatedDocumentDto.getTemplateId())) {
            throw new ForeignKeyViolationException("templateId",
                    "Document template not found with id: " + generatedDocumentDto.getTemplateId());
        }

        // Validate version ID if provided
        if (generatedDocumentDto.getVersionId() != null &&
                !documentTemplateVersionRepository.existsById(generatedDocumentDto.getVersionId())) {
            throw new ForeignKeyViolationException("versionId",
                    "Document template version not found with id: " + generatedDocumentDto.getVersionId());
        }

        // Validate client ID if provided
        if (generatedDocumentDto.getClientId() != null &&
                !clientRepository.existsById(generatedDocumentDto.getClientId())) {
            throw new ForeignKeyViolationException("clientId",
                    "Client not found with id: " + generatedDocumentDto.getClientId());
        }

        // Validate deal ID if provided
        if (generatedDocumentDto.getDealId() != null &&
                !dealRepository.existsById(generatedDocumentDto.getDealId())) {
            throw new ForeignKeyViolationException("dealId",
                    "Deal not found with id: " + generatedDocumentDto.getDealId());
        }

        // Validate status if provided
        if (!StringUtils.hasText(generatedDocumentDto.getStatus())) {
            throw new NullConstraintViolationException("status", "Status cannot be empty");
        }
    }

    @Override
    @Transactional
    public GeneratedDocumentDto createGeneratedDocument(GeneratedDocumentDto generatedDocumentDto) {
        validateGeneratedDocumentDto(generatedDocumentDto);

        try {
            GeneratedDocument generatedDocument = convertToEntity(generatedDocumentDto);
            GeneratedDocument savedGeneratedDocument = generatedDocumentRepository.save(generatedDocument);
            return convertToDto(savedGeneratedDocument);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("field",
                        "Generated document with these details already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating generated document: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating generated document", e);
        }
    }

    @Override
    @Transactional
    public GeneratedDocumentDto updateGeneratedDocument(Long id, GeneratedDocumentDto generatedDocumentDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Generated document ID cannot be null");
        }

        GeneratedDocument existingGeneratedDocument = generatedDocumentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Generated Document not found with id: " + id));

        // Validate foreign keys in the DTO
        if (generatedDocumentDto.getTemplateId() != null &&
                !documentTemplateRepository.existsById(generatedDocumentDto.getTemplateId())) {
            throw new ForeignKeyViolationException("templateId",
                    "Document template not found with id: " + generatedDocumentDto.getTemplateId());
        }

        if (generatedDocumentDto.getVersionId() != null &&
                !documentTemplateVersionRepository.existsById(generatedDocumentDto.getVersionId())) {
            throw new ForeignKeyViolationException("versionId",
                    "Document template version not found with id: " + generatedDocumentDto.getVersionId());
        }

        if (generatedDocumentDto.getClientId() != null &&
                !clientRepository.existsById(generatedDocumentDto.getClientId())) {
            throw new ForeignKeyViolationException("clientId",
                    "Client not found with id: " + generatedDocumentDto.getClientId());
        }

        if (generatedDocumentDto.getDealId() != null &&
                !dealRepository.existsById(generatedDocumentDto.getDealId())) {
            throw new ForeignKeyViolationException("dealId",
                    "Deal not found with id: " + generatedDocumentDto.getDealId());
        }

        try {
            updateGeneratedDocumentFromDto(existingGeneratedDocument, generatedDocumentDto);
            GeneratedDocument updatedGeneratedDocument = generatedDocumentRepository.save(existingGeneratedDocument);
            return convertToDto(updatedGeneratedDocument);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("field",
                        "Generated document with these details already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating generated document: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating generated document", e);
        }
    }

    @Override
    @Transactional
    public GeneratedDocumentDto updateGeneratedDocumentStatus(Long id, String status) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Generated document ID cannot be null");
        }

        if (!StringUtils.hasText(status)) {
            throw new NullConstraintViolationException("status", "Status cannot be empty");
        }

        GeneratedDocument generatedDocument = generatedDocumentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Generated Document not found with id: " + id));

        try {
            generatedDocument.setStatus(status);
            GeneratedDocument updatedGeneratedDocument = generatedDocumentRepository.save(generatedDocument);
            return convertToDto(updatedGeneratedDocument);
        } catch (Exception e) {
            throw new CustomException("Error updating generated document status: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void deleteGeneratedDocument(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Generated document ID cannot be null");
        }

        if (!generatedDocumentRepository.existsById(id)) {
            throw new ResourceNotFoundException("Generated Document not found with id: " + id);
        }

        try {
            generatedDocumentRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete generated document because it is referenced by other entities",
                        e);
            } else {
                throw new CustomException("Error deleting generated document: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting generated document", e);
        }
    }

    private GeneratedDocumentDto convertToDto(GeneratedDocument generatedDocument) {
        return GeneratedDocumentDto.builder()
                .id(generatedDocument.getId())
                .templateId(generatedDocument.getTemplate() != null ? generatedDocument.getTemplate().getId() : null)
                .versionId(generatedDocument.getVersion() != null ? generatedDocument.getVersion().getId() : null)
                .clientId(generatedDocument.getClient() != null ? generatedDocument.getClient().getId() : null)
                .dealId(generatedDocument.getDeal() != null ? generatedDocument.getDeal().getId() : null)
                .filePath(generatedDocument.getFilePath())
                .filledContent(generatedDocument.getFilledContent())
                .status(generatedDocument.getStatus())
                .build();
    }

    private GeneratedDocument convertToEntity(GeneratedDocumentDto generatedDocumentDto) {
        GeneratedDocument generatedDocument = new GeneratedDocument();
        generatedDocument.setId(generatedDocumentDto.getId());

        updateGeneratedDocumentFromDto(generatedDocument, generatedDocumentDto);

        return generatedDocument;
    }

    private void updateGeneratedDocumentFromDto(GeneratedDocument generatedDocument,
            GeneratedDocumentDto generatedDocumentDto) {
        if (generatedDocumentDto.getTemplateId() != null) {
            DocumentTemplate documentTemplate = documentTemplateRepository
                    .findById(generatedDocumentDto.getTemplateId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Document Template not found with id: " + generatedDocumentDto.getTemplateId()));
            generatedDocument.setTemplate(documentTemplate);
        }

        if (generatedDocumentDto.getVersionId() != null) {
            DocumentTemplateVersion documentTemplateVersion = documentTemplateVersionRepository
                    .findById(generatedDocumentDto.getVersionId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Document Template Version not found with id: " + generatedDocumentDto.getVersionId()));
            generatedDocument.setVersion(documentTemplateVersion);
        }

        if (generatedDocumentDto.getClientId() != null) {
            Client client = clientRepository.findById(generatedDocumentDto.getClientId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Client not found with id: " + generatedDocumentDto.getClientId()));
            generatedDocument.setClient(client);
        }

        if (generatedDocumentDto.getDealId() != null) {
            Deal deal = dealRepository.findById(generatedDocumentDto.getDealId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Deal not found with id: " + generatedDocumentDto.getDealId()));
            generatedDocument.setDeal(deal);
        }

        if (StringUtils.hasText(generatedDocumentDto.getFilePath())) {
            generatedDocument.setFilePath(generatedDocumentDto.getFilePath());
        }

        if (StringUtils.hasText(generatedDocumentDto.getFilledContent())) {
            generatedDocument.setFilledContent(generatedDocumentDto.getFilledContent());
        }

        if (StringUtils.hasText(generatedDocumentDto.getStatus())) {
            generatedDocument.setStatus(generatedDocumentDto.getStatus());
        }
    }
}
