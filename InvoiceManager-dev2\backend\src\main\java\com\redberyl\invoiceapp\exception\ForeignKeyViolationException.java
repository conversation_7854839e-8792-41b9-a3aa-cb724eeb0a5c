package com.redberyl.invoiceapp.exception;

import lombok.Getter;

/**
 * Exception thrown when a foreign key constraint is violated.
 * This typically happens when trying to reference a non-existent entity
 * or when trying to delete an entity that is referenced by other entities.
 */
@Getter
public class ForeignKeyViolationException extends RuntimeException {
    
    private final String fieldName;
    
    public ForeignKeyViolationException(String fieldName, String message) {
        super(message);
        this.fieldName = fieldName;
    }
    
    public ForeignKeyViolationException(String message) {
        super(message);
        this.fieldName = "unknown";
    }
}
