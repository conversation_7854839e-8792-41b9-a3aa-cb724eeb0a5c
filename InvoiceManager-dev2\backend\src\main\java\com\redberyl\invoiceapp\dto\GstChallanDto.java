package com.redberyl.invoiceapp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GstChallanDto {
    
    private String challanNumber;
    private LocalDate challanDate;
    private String month;
    private List<GstChallanEntryDto> entries;
    private BigDecimal totalBillAmount;
    private BigDecimal totalCgst;
    private BigDecimal totalSgst;
    private BigDecimal totalIgst;
    private BigDecimal totalInvoiceAmount;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GstChallanEntryDto {
        private String candidateName;  // Changed from clientName to candidateName
        private String clientName;     // Added client name field
        private String clientAddress;
        private String clientGstNo;
        private String invoiceNo;
        private LocalDate invoiceDate;
        private String invoiceMonth;
        private BigDecimal billAmount;
        private BigDecimal cgst;
        private BigDecimal sgst;
        private BigDecimal igst;
        private BigDecimal invoiceAmount;
    }
}
