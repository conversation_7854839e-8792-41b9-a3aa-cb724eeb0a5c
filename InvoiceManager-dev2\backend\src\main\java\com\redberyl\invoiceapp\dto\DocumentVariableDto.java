package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.redberyl.invoiceapp.util.IdConverter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class DocumentVariableDto extends BaseDto {
    private Long id;

    @NotNull(message = "Template version ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Object templateVersionId;

    // Include the full template version object in the response
    private DocumentTemplateVersionDto templateVersion;

    public Long getTemplateVersionId() {
        return templateVersionId != null ? IdConverter.extractId(templateVersionId) : null;
    }

    public void setTemplateVersionId(Object templateVersionId) {
        this.templateVersionId = templateVersionId;
    }

    @NotBlank(message = "Variable name is required")
    private String variableName;

    private String description;
    private String sampleValue;
}
