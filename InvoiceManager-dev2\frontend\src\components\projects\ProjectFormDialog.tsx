import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

interface ProjectFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId?: string;
  defaultValues?: {
    name: string;
    client: string;
    description?: string;
    email?: string;
    phone?: string;
    gstNumber?: string;
    billingAddress?: string;
    shippingAddress?: string;
    state?: string;
    engagementCode?: string;
    clientPartnerName?: string;
    clientPartnerEmail?: string;
    clientPartnerPhone?: string;
    bdm?: string;
    commissionPercentage?: string;
    commissionAmount?: string;
    hsnCode?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    value?: string;
  };
  clients?: { id: string; name: string }[];
  bdms?: { id: string; name: string }[];
  hsnCodes?: { id: string; code: string; description: string }[];
  onSave?: (projectData: any) => Promise<void>;
}

const ProjectFormDialog: React.FC<ProjectFormDialogProps> = ({
  open,
  onOpenChange,
  projectId,
  defaultValues,
  clients = [],
  bdms = [],
  hsnCodes = []
}) => {
  const isEditing = !!projectId;
  const [activeTab, setActiveTab] = useState("basic");

  const [formData, setFormData] = useState({
    name: "",
    client: "",
    description: "",
    email: "",
    phone: "",
    gstNumber: "",
    billingAddress: "",
    shippingAddress: "",
    state: "",
    engagementCode: "",
    clientPartnerName: "",
    clientPartnerEmail: "",
    clientPartnerPhone: "",
    bdm: "",
    commissionPercentage: "",
    commissionAmount: "",
    hsnCode: "",
    startDate: "",
    endDate: "",
    status: "Pending",
    value: ""
  });

  // Reset form data when dialog opens/closes or when defaultValues change
  useEffect(() => {
    if (open) {
      if (defaultValues) {
        // When editing, populate with existing data
        console.log("Populating project form with data:", defaultValues);
        const initialFormData = {
          name: "",
          client: "",
          description: "",
          email: "",
          phone: "",
          gstNumber: "",
          billingAddress: "",
          shippingAddress: "",
          state: "",
          engagementCode: "",
          clientPartnerName: "",
          clientPartnerEmail: "",
          clientPartnerPhone: "",
          bdm: "",
          commissionPercentage: "",
          commissionAmount: "",
          hsnCode: "",
          startDate: "",
          endDate: "",
          status: "Pending",
          value: ""
        };

        setFormData({
          ...initialFormData,
          ...defaultValues
        });
      } else {
        // When creating new, reset to empty form
        setFormData({
          name: "",
          client: "",
          description: "",
          email: "",
          phone: "",
          gstNumber: "",
          billingAddress: "",
          shippingAddress: "",
          state: "",
          engagementCode: "",
          clientPartnerName: "",
          clientPartnerEmail: "",
          clientPartnerPhone: "",
          bdm: "",
          commissionPercentage: "",
          commissionAmount: "",
          hsnCode: "",
          startDate: "",
          endDate: "",
          status: "Pending",
          value: ""
        });
      }
    }
  }, [open, defaultValues]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form - only basic fields are required
    if (!formData.name || !formData.client) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Format the value to ensure it has a dollar sign if provided
    let finalFormData = { ...formData };
    if (formData.value && !formData.value.startsWith("$")) {
      finalFormData.value = `$${formData.value}`;
    }

    // Calculate commission amount if percentage is provided but amount is not
    if (formData.commissionPercentage && !formData.commissionAmount && formData.value) {
      const valueNumber = parseFloat(formData.value.replace(/[^0-9.]/g, ''));
      const percentageNumber = parseFloat(formData.commissionPercentage);
      if (!isNaN(valueNumber) && !isNaN(percentageNumber)) {
        const commissionAmount = (valueNumber * percentageNumber / 100).toFixed(2);
        finalFormData.commissionAmount = commissionAmount;
      }
    }

    // Find the client ID from the client name
    const selectedClient = clients.find(c => c.name === formData.client);
    const clientId = selectedClient?.id;

    // Prepare the data for the API
    const projectApiData = {
      ...finalFormData,
      clientId: clientId,
      // Convert dates to proper format if provided
      startDate: finalFormData.startDate ? finalFormData.startDate : null,
      endDate: finalFormData.endDate ? finalFormData.endDate : null,
      // Convert numeric values
      value: finalFormData.value ? parseFloat(finalFormData.value.replace(/[^0-9.]/g, '')) : null,
      commissionPercentage: finalFormData.commissionPercentage ? parseFloat(finalFormData.commissionPercentage) : null,
      commissionAmount: finalFormData.commissionAmount ? parseFloat(finalFormData.commissionAmount) : null
    };

    try {
      // Show loading toast
      const loadingToast = toast.loading(isEditing ? "Updating project..." : "Creating project...");

      if (onSave) {
        // Use the provided onSave function
        await onSave(projectApiData);

        // Update the toast
        toast.success(isEditing ? `Project "${formData.name}" updated successfully` : `Project "${formData.name}" created successfully`, {
          id: loadingToast
        });
      } else {
        // Direct API call through the proxy if no onSave function is provided
        const url = isEditing
          ? `/api/projects/update/${projectId}`
          : '/api/projects/create';

        const response = await fetch(url, {
          method: isEditing ? 'PUT' : 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': 'Basic ' + btoa('admin:admin123')
          },
          body: JSON.stringify(projectApiData)
        });

        if (!response.ok) {
          let errorMessage = "";
          try {
            const errorJson = await response.json();
            errorMessage = errorJson.message || errorJson.error || JSON.stringify(errorJson);
          } catch (e) {
            const errorText = await response.text();
            errorMessage = errorText || `Server returned ${response.status}`;
          }

          throw new Error(`Failed to ${isEditing ? 'update' : 'create'} project: ${errorMessage}`);
        }

        // Update the toast
        toast.success(isEditing ? `Project "${formData.name}" updated successfully` : `Project "${formData.name}" created successfully`, {
          id: loadingToast
        });
      }

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      console.error("Error saving project:", error);
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} project`, {
        description: error instanceof Error ? error.message : "Unknown error"
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Project" : "Add New Project"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="contact">Contact Details</TabsTrigger>
              <TabsTrigger value="financial">Financial</TabsTrigger>
              <TabsTrigger value="timeline">Timeline</TabsTrigger>
            </TabsList>

            {/* Basic Info Tab */}
            <TabsContent value="basic" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Project Name <span className="text-red-500">*</span></Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Enter project name"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="client">Client <span className="text-red-500">*</span></Label>
                  <Select
                    value={formData.client}
                    onValueChange={(value) => handleSelectChange("client", value)}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a client" />
                    </SelectTrigger>
                    <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md">
                      {clients.length > 0 ? (
                        clients.map((client) => (
                          <SelectItem key={client.id} value={client.name} className="cursor-pointer hover:bg-gray-100">
                            {client.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="Acme Corporation" className="cursor-pointer hover:bg-gray-100">Acme Corporation</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Enter project description"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="engagementCode">Engagement Code</Label>
                  <Input
                    id="engagementCode"
                    name="engagementCode"
                    value={formData.engagementCode}
                    onChange={handleChange}
                    placeholder="Enter engagement code"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="hsnCode">HSN Code</Label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        toast.success("Refreshing HSN codes...");
                      }}
                      className="h-6 px-2"
                    >
                      <Loader2 className="h-3 w-3 mr-1" />
                      Refresh
                    </Button>
                  </div>
                  <Select
                    value={formData.hsnCode}
                    onValueChange={(value) => handleSelectChange("hsnCode", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select HSN code" />
                    </SelectTrigger>
                    <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md z-[9999]">
                      {hsnCodes.length > 0 ? (
                        hsnCodes.map((code) => (
                          <SelectItem key={code.id} value={code.code} className="cursor-pointer hover:bg-gray-100">
                            {code.code}
                          </SelectItem>
                        ))
                      ) : (
                        <>
                          <SelectItem value="998313" className="cursor-pointer hover:bg-gray-100">998313</SelectItem>
                          <SelectItem value="998314" className="cursor-pointer hover:bg-gray-100">998314</SelectItem>
                          <SelectItem value="998316" className="cursor-pointer hover:bg-gray-100">998316</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            {/* Contact Details Tab */}
            <TabsContent value="contact" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="+****************"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gstNumber">GST Number</Label>
                  <Input
                    id="gstNumber"
                    name="gstNumber"
                    value={formData.gstNumber}
                    onChange={handleChange}
                    placeholder="Enter GST number"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="billingAddress">Billing Address</Label>
                  <Textarea
                    id="billingAddress"
                    name="billingAddress"
                    value={formData.billingAddress}
                    onChange={handleChange}
                    placeholder="Enter billing address"
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="shippingAddress">Shipping Address</Label>
                  <Textarea
                    id="shippingAddress"
                    name="shippingAddress"
                    value={formData.shippingAddress}
                    onChange={handleChange}
                    placeholder="Enter shipping address"
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="state">State (For GST Calculation)</Label>
                  <select
                    id="state"
                    name="state"
                    value={formData.state}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select State</option>
                    <option value="Maharashtra">Maharashtra</option>
                    <option value="Karnataka">Karnataka</option>
                    <option value="Tamil Nadu">Tamil Nadu</option>
                    <option value="Gujarat">Gujarat</option>
                    <option value="Rajasthan">Rajasthan</option>
                    <option value="Uttar Pradesh">Uttar Pradesh</option>
                    <option value="West Bengal">West Bengal</option>
                    <option value="Madhya Pradesh">Madhya Pradesh</option>
                    <option value="Bihar">Bihar</option>
                    <option value="Odisha">Odisha</option>
                    <option value="Telangana">Telangana</option>
                    <option value="Andhra Pradesh">Andhra Pradesh</option>
                    <option value="Kerala">Kerala</option>
                    <option value="Haryana">Haryana</option>
                    <option value="Punjab">Punjab</option>
                    <option value="Assam">Assam</option>
                    <option value="Jharkhand">Jharkhand</option>
                    <option value="Chhattisgarh">Chhattisgarh</option>
                    <option value="Himachal Pradesh">Himachal Pradesh</option>
                    <option value="Uttarakhand">Uttarakhand</option>
                    <option value="Goa">Goa</option>
                    <option value="Tripura">Tripura</option>
                    <option value="Meghalaya">Meghalaya</option>
                    <option value="Manipur">Manipur</option>
                    <option value="Nagaland">Nagaland</option>
                    <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                    <option value="Mizoram">Mizoram</option>
                    <option value="Sikkim">Sikkim</option>
                    <option value="Delhi">Delhi</option>
                    <option value="Puducherry">Puducherry</option>
                    <option value="Chandigarh">Chandigarh</option>
                    <option value="Andaman and Nicobar Islands">Andaman and Nicobar Islands</option>
                    <option value="Dadra and Nagar Haveli and Daman and Diu">Dadra and Nagar Haveli and Daman and Diu</option>
                    <option value="Lakshadweep">Lakshadweep</option>
                    <option value="Ladakh">Ladakh</option>
                    <option value="Jammu and Kashmir">Jammu and Kashmir</option>
                  </select>
                  <p className="text-sm text-gray-600">
                    Maharashtra clients: CGST + SGST (9% each) | Other states: IGST (18%)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clientPartnerName">Client Partner Name</Label>
                  <Input
                    id="clientPartnerName"
                    name="clientPartnerName"
                    value={formData.clientPartnerName}
                    onChange={handleChange}
                    placeholder="Enter client partner name"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="clientPartnerEmail">Client Partner Email</Label>
                    <Input
                      id="clientPartnerEmail"
                      name="clientPartnerEmail"
                      type="email"
                      value={formData.clientPartnerEmail}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="clientPartnerPhone">Client Partner Phone</Label>
                    <Input
                      id="clientPartnerPhone"
                      name="clientPartnerPhone"
                      value={formData.clientPartnerPhone}
                      onChange={handleChange}
                      placeholder="+****************"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Financial Tab */}
            <TabsContent value="financial" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="value">Project Value</Label>
                  <Input
                    id="value"
                    name="value"
                    value={formData.value}
                    onChange={handleChange}
                    placeholder="Enter project value (e.g. 10000)"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bdm">Business Development Manager (BDM)</Label>
                  <Select
                    value={formData.bdm}
                    onValueChange={(value) => handleSelectChange("bdm", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select BDM" />
                    </SelectTrigger>
                    <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md">
                      {bdms.length > 0 ? (
                        bdms.map((bdm) => (
                          <SelectItem key={bdm.id} value={bdm.name} className="cursor-pointer hover:bg-gray-100">
                            {bdm.name}
                          </SelectItem>
                        ))
                      ) : (
                        <>
                          <SelectItem value="Sarah Johnson" className="cursor-pointer hover:bg-gray-100">Sarah Johnson</SelectItem>
                          <SelectItem value="Michael Brown" className="cursor-pointer hover:bg-gray-100">Michael Brown</SelectItem>
                          <SelectItem value="Emily Davis" className="cursor-pointer hover:bg-gray-100">Emily Davis</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="commissionPercentage">Commission Percentage (%)</Label>
                    <Input
                      id="commissionPercentage"
                      name="commissionPercentage"
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      value={formData.commissionPercentage}
                      onChange={handleChange}
                      placeholder="e.g. 5.00"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="commissionAmount">Commission Amount</Label>
                    <Input
                      id="commissionAmount"
                      name="commissionAmount"
                      value={formData.commissionAmount}
                      onChange={handleChange}
                      placeholder="Calculated from percentage"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Timeline Tab */}
            <TabsContent value="timeline" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startDate">Start Date</Label>
                    <Input
                      id="startDate"
                      name="startDate"
                      type="date"
                      value={formData.startDate}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="endDate">End Date</Label>
                    <Input
                      id="endDate"
                      name="endDate"
                      type="date"
                      value={formData.endDate}
                      onChange={handleChange}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleSelectChange("status", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md">
                      <SelectItem value="Pending" className="cursor-pointer hover:bg-gray-100">Pending</SelectItem>
                      <SelectItem value="In Progress" className="cursor-pointer hover:bg-gray-100">In Progress</SelectItem>
                      <SelectItem value="On Hold" className="cursor-pointer hover:bg-gray-100">On Hold</SelectItem>
                      <SelectItem value="Completed" className="cursor-pointer hover:bg-gray-100">Completed</SelectItem>
                      <SelectItem value="Cancelled" className="cursor-pointer hover:bg-gray-100">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">{isEditing ? "Update Project" : "Create Project"}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ProjectFormDialog;
