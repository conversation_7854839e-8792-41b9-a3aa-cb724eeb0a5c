import { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { Eye, EyeOff, Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import BackendStatusChecker from "@/components/BackendStatusChecker";
import { getApiUrl } from "@/utils/ipUtils";

const Login = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { login, isAuthenticated, loading: authLoading } = useAuth();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      navigate("/");
    }
  }, [isAuthenticated, authLoading, navigate]);

  // Pre-fill username if remembered
  useEffect(() => {
    const savedUsername = localStorage.getItem("username");
    if (savedUsername) {
      setUsername(savedUsername);
      setRememberMe(true);
    }
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage(null);

    if (!username || !password) {
      setErrorMessage("Please enter both username and password");
      toast({
        title: "Error",
        description: "Please enter both username and password",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      console.log("Attempting to login with:", { username, password, rememberMe });

      // Direct fetch approach - bypassing the API utility
      console.log("Sending login request to backend...");

      let response;
      try {
        // Log the request details for debugging
        const requestBody = {
          username,
          password
        };
        console.log("Request payload:", requestBody);

        // Use the proxy defined in vite.config.ts to avoid CORS issues
        // This will route through the frontend server which will handle CORS
        const loginUrl = "/api/auth/login";

        console.log("Using proxy for login URL:", loginUrl);

        response = await fetch(loginUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            // "Accept": "application/json",
            "Access-Control-Allow-Origin": "*"
          },
          body: JSON.stringify(requestBody),
          credentials: "include", // Include credentials when using the proxy
          mode: "cors"
        });

        console.log("Received response from backend:", response.status, response.statusText);
      } catch (error) {
        console.error("Network error during fetch:", error);

        // Check if it's a CORS error
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes("NetworkError") ||
            errorMessage.includes("Failed to fetch") ||
            errorMessage.includes("CORS")) {
          throw new Error(
            "CORS Error: The server rejected the request. Please check if the backend server is running and CORS is properly configured."
          );
        }

        throw new Error("Network error: Failed to connect to the server. Please check if the backend server is running.");
      }

      console.log("Login response status:", response.status);

      // Log response headers for debugging
      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });
      console.log('Response headers:', responseHeaders);

      if (!response.ok) {
        let errorMessage = "Login failed";
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          console.error("Error parsing error response:", e);
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log("Login result:", result);

      // Store the token and user info
      if (result.token) {
        localStorage.setItem("token", result.token);
        if (rememberMe) {
          localStorage.setItem("username", username);
        } else {
          localStorage.removeItem("username");
        }

        // Update auth context
        login(username, password, rememberMe);
      }

      toast({
        title: "Success",
        description: "Login successful",
      });

      // Short delay before redirecting
      setTimeout(() => {
        navigate("/");
      }, 1000);
    } catch (error: any) {
      console.error("Login error:", error);
      const message = error.message || "Invalid username or password";
      setErrorMessage(message);
      toast({
        title: "Error",
        description: message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const [isBackendOnline, setIsBackendOnline] = useState<boolean | null>(null);

  const handleBackendStatusChange = (isOnline: boolean) => {
    setIsBackendOnline(isOnline);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="w-full max-w-md p-4">
        <BackendStatusChecker
          backendUrl="/api"
          onStatusChange={handleBackendStatusChange}
          hideUI={true}
        />

        <Card className="border-0 shadow-lg">
          <CardHeader className="space-y-1 text-center">
            <div className="flex justify-center mb-4">
              <img
                src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQwIiBoZWlnaHQ9IjQ4IiB2aWV3Qm94PSIwIDAgMjQwIDQ4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSI0OCIgaGVpZ2h0PSI0OCIgZmlsbD0iIzQ5OTBlMiIvPjxwYXRoIGQ9Ik0xMiAxMkgzNlYzNkgxMlYxMloiIGZpbGw9IndoaXRlIi8+PHRleHQgeD0iNTYiIHk9IjMyIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSIjMzMzMzMzIj5SZWRiZXJ5bDwvdGV4dD48L3N2Zz4="
                alt="Redberyl Logo"
                className="h-12"
              />
            </div>
            <CardTitle className="text-2xl font-bold">Sign In</CardTitle>
            <CardDescription>
              Enter your credentials to access your account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  placeholder="Enter your username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="password">Password</Label>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-xs font-normal"
                    type="button"
                    onClick={() => navigate("/auth/forgot-password")}
                  >
                    Forgot password?
                  </Button>
                </div>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(checked === true)}
                />
                <Label htmlFor="remember" className="text-sm font-normal">
                  Remember me
                </Label>
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  "Sign In"
                )}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            {errorMessage && (
              <div className="text-center text-sm text-red-500 font-medium">
                {errorMessage}
              </div>
            )}
            <div className="text-center text-sm text-gray-500">
              Don't have an account?{" "}
              <Button
                variant="link"
                className="p-0 h-auto"
                onClick={() => navigate("/auth/signup")}
              >
                Sign up
              </Button>
            </div>
            <div className="text-center text-xs text-gray-500">
              © {new Date().getFullYear()} Redberyl. All rights reserved.
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Login;
