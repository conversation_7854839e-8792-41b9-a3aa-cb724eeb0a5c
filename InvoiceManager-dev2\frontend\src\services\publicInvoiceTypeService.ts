/**
 * Public Invoice Type Service
 * 
 * A very simple service that directly fetches invoice types from the public endpoint
 * with no authentication or complex logic.
 */

export interface PublicInvoiceType {
  id: string | number;
  invoiceType: string;
  typeDesc: string;
}

/**
 * Public Invoice Type Service
 */
export const publicInvoiceTypeService = {
  /**
   * Get all invoice types from the public endpoint
   */
  getAllInvoiceTypes: async (): Promise<PublicInvoiceType[]> => {
    console.log('PublicInvoiceTypeService: Starting fetch');
    
    try {
      // Make a simple fetch request to the public endpoint
      const response = await fetch('http://localhost:8091/public/invoice-types', {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch invoice types: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('PublicInvoiceTypeService: Received data:', data);
      
      if (!data || !Array.isArray(data)) {
        throw new Error('Invalid response format');
      }
      
      return data;
    } catch (error) {
      console.error('PublicInvoiceTypeService: Error fetching invoice types:', error);
      
      // Return empty array on error
      return [];
    }
  }
};
