import React, { useState, useRef, useEffect } from 'react';
import { MoreHorizontal, Edit, FileText, Trash } from 'lucide-react';

interface ActionMenuWithIconsProps {
  onEdit?: () => void;
  onView?: () => void;
  onDelete?: () => void;
  viewLabel?: string;
}

const ActionMenuWithIcons: React.FC<ActionMenuWithIconsProps> = ({
  onEdit,
  onView,
  onDelete,
  viewLabel = 'View Invoices'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close the menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="relative" ref={menuRef}>
      {/* Trigger button */}
      <button
        type="button"
        className="inline-flex items-center justify-center rounded-md text-sm font-medium h-8 w-8 text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-300"
        onClick={() => setIsOpen(!isOpen)}
      >
        <MoreHorizontal className="h-4 w-4" />
        <span className="sr-only">Open menu</span>
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-2 px-3 border-b border-gray-200">
            <div className="text-sm font-semibold">Actions</div>
          </div>
          <div className="py-1">
            {onEdit && (
              <button
                type="button"
                className="flex w-full items-center px-3 py-2 text-sm text-amber-500 hover:bg-amber-50"
                onClick={() => {
                  setIsOpen(false);
                  setTimeout(() => onEdit(), 10);
                }}
              >
                <Edit className="h-4 w-4 mr-2" />
                <span>Edit</span>
              </button>
            )}

            {onView && (
              <button
                type="button"
                className="flex w-full items-center px-3 py-2 text-sm text-blue-500 hover:bg-blue-50"
                onClick={() => {
                  setIsOpen(false);
                  setTimeout(() => onView(), 10);
                }}
              >
                <FileText className="h-4 w-4 mr-2" />
                <span>{viewLabel}</span>
              </button>
            )}

            {onDelete && (
              <>
                <hr className="my-1 border-gray-200" />
                <button
                  type="button"
                  className="flex w-full items-center px-3 py-2 text-sm text-red-500 hover:bg-red-50"
                  onClick={() => {
                    setIsOpen(false);
                    setTimeout(() => onDelete(), 10);
                  }}
                >
                  <Trash className="h-4 w-4 mr-2" />
                  <span>Delete</span>
                </button>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ActionMenuWithIcons;
