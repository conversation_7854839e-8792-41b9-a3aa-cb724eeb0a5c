/**
 * HSN Code Validation Utilities
 * 
 * HSN (Harmonized System of Nomenclature) codes must be exactly 6 digits
 * as per Indian GST regulations.
 */

export interface HsnCodeValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Validates if an HSN code is in the correct format
 * @param hsnCode The HSN code to validate
 * @returns Validation result with error message if invalid
 */
export const validateHsnCode = (hsnCode: string): HsnCodeValidationResult => {
  // Remove any whitespace
  const trimmedCode = hsnCode?.trim() || '';

  // Check if empty
  if (!trimmedCode) {
    return {
      isValid: false,
      error: 'HSN code is required'
    };
  }

  // Check if exactly 6 digits
  const hsnCodeRegex = /^[0-9]{6}$/;
  if (!hsnCodeRegex.test(trimmedCode)) {
    if (trimmedCode.length < 6) {
      return {
        isValid: false,
        error: `HSN code must be exactly 6 digits. Current length: ${trimmedCode.length}`
      };
    } else if (trimmedCode.length > 6) {
      return {
        isValid: false,
        error: `HSN code must be exactly 6 digits. Current length: ${trimmedCode.length}`
      };
    } else {
      return {
        isValid: false,
        error: 'HSN code must contain only digits (0-9)'
      };
    }
  }

  return {
    isValid: true
  };
};

/**
 * Formats an HSN code by removing non-digits and limiting to 6 characters
 * @param input The input string to format
 * @returns Formatted HSN code (digits only, max 6 characters)
 */
export const formatHsnCodeInput = (input: string): string => {
  return input.replace(/[^0-9]/g, '').slice(0, 6);
};

/**
 * Checks if an HSN code is valid (exactly 6 digits)
 * @param hsnCode The HSN code to check
 * @returns True if valid, false otherwise
 */
export const isValidHsnCode = (hsnCode: string): boolean => {
  return validateHsnCode(hsnCode).isValid;
};

/**
 * Common HSN codes for IT services (for reference)
 */
export const COMMON_HSN_CODES = {
  IT_CONSULTING: '998313',
  SOFTWARE_DEVELOPMENT: '998314', 
  DATA_PROCESSING: '998316',
  OTHER_IT_SERVICES: '998300'
} as const;

/**
 * Gets a user-friendly description for common HSN codes
 * @param hsnCode The HSN code
 * @returns Description if known, undefined otherwise
 */
export const getHsnCodeDescription = (hsnCode: string): string | undefined => {
  const descriptions: Record<string, string> = {
    '998313': 'IT consulting services',
    '998314': 'Software development services',
    '998316': 'Data processing services',
    '998300': 'Other IT services'
  };
  
  return descriptions[hsnCode];
};
