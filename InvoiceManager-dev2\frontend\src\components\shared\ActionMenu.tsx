import React, { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Edit, Trash, MoreHorizontal, Eye, FileText } from "lucide-react";
import DeleteConfirmationDialog from "./DeleteConfirmationDialog";

export interface ActionMenuProps {
  itemId: string;
  onEdit?: (id: string) => void;
  onView?: (id: string) => void;
  onDelete?: (id: string) => void;
  onGenerate?: (id: string) => void;
  viewLabel?: string;
  editLabel?: string;
  deleteLabel?: string;
  generateLabel?: string;
  deleteDialogTitle?: string;
  deleteDialogDescription?: string;
  showGenerate?: boolean;
}

const ActionMenu: React.FC<ActionMenuProps> = ({
  itemId,
  onEdit,
  onView,
  onDelete,
  onGenerate,
  viewLabel = "View Details",
  editLabel = "Edit",
  deleteLabel = "Delete",
  generateLabel = "Generate Invoice",
  deleteDialogTitle = "Delete Item",
  deleteDialogDescription = "Are you sure you want to delete this item? This action cannot be undone and will remove all associated data.",
  showGenerate = false,
}) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDelete = () => {
    if (onDelete) {
      onDelete(itemId);
    }
    setIsDeleteDialogOpen(false);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          {onView && (
            <DropdownMenuItem
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onView(itemId);
              }}
              className="cursor-pointer"
            >
              <Eye className="mr-2 h-4 w-4 text-blue-600" />
              <span>{viewLabel}</span>
            </DropdownMenuItem>
          )}

          {onEdit && (
            <DropdownMenuItem
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onEdit(itemId);
              }}
              className="cursor-pointer"
            >
              <Edit className="mr-2 h-4 w-4 text-amber-600" />
              <span>{editLabel}</span>
            </DropdownMenuItem>
          )}

          {showGenerate && onGenerate && (
            <DropdownMenuItem
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onGenerate(itemId);
              }}
              className="cursor-pointer"
            >
              <FileText className="mr-2 h-4 w-4 text-green-600" />
              <span>{generateLabel}</span>
            </DropdownMenuItem>
          )}

          {onDelete && (
            <DropdownMenuItem
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setIsDeleteDialogOpen(true);
              }}
              className="cursor-pointer text-red-600"
            >
              <Trash className="mr-2 h-4 w-4" />
              <span>{deleteLabel}</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {onDelete && (
        <DeleteConfirmationDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onConfirm={handleDelete}
          title={deleteDialogTitle}
          description={deleteDialogDescription}
        />
      )}
    </>
  );
};

export default ActionMenu;
