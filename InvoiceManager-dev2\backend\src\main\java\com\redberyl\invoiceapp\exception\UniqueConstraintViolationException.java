package com.redberyl.invoiceapp.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.CONFLICT)
public class UniqueConstraintViolationException extends RuntimeException {

    private final String fieldName;
    private final Object fieldValue;

    public UniqueConstraintViolationException(String fieldName, Object fieldValue) {
        super(String.format("Entity with %s: '%s' already exists", fieldName, fieldValue));
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
    }

    public UniqueConstraintViolationException(String fieldName, String message) {
        super(message);
        this.fieldName = fieldName;
        this.fieldValue = null;
    }

    public String getFieldName() {
        return fieldName;
    }

    public Object getFieldValue() {
        return fieldValue;
    }
}
