package com.redberyl.invoiceapp.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@CrossOrigin(origins = { "http://localhost:3060", "http://127.0.0.1:3060" }, allowedHeaders = "*", methods = {
        RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT,
        RequestMethod.DELETE, RequestMethod.OPTIONS }, allowCredentials = "true", maxAge = 3600)
@RestController

@Tag(name = "Simplified Collection", description = "Simplified Collection API")
@SecurityRequirement(name = "bearer-jwt")
public class SimplifiedCollectionController {

    @GetMapping("/simplified-collections/getById/{id}")
    @Operation(summary = "Get collection by ID in simplified format", description = "Get collection by ID in simplified format")
    public ResponseEntity<?> getById(
            @Parameter(description = "Collection ID", required = true) @PathVariable("id") Long id) {
        // This is a placeholder implementation
        Map<String, Object> response = new HashMap<>();
        response.put("id", id);
        response.put("name", "Sample Collection");
        response.put("description", "This is a sample collection");
        response.put("items", new Object[] {
                Map.of("id", 1, "name", "Item 1"),
                Map.of("id", 2, "name", "Item 2")
        });

        return ResponseEntity.ok(response);
    }

    @GetMapping("/simplified-collections/getByApiId/{apiId}")
    @Operation(summary = "Get collection by API ID in simplified format", description = "Get collection by API ID in simplified format")
    public ResponseEntity<?> getByApiId(
            @Parameter(description = "API ID", required = true) @PathVariable("apiId") String apiId) {
        // This is a placeholder implementation
        Map<String, Object> response = new HashMap<>();
        response.put("apiId", apiId);
        response.put("name", "Sample Collection");
        response.put("description", "This is a sample collection");
        response.put("items", new Object[] {
                Map.of("id", 1, "name", "Item 1"),
                Map.of("id", 2, "name", "Item 2")
        });

        return ResponseEntity.ok(response);
    }

    @GetMapping("/simplified-collections/getAll")
    @Operation(summary = "Get all collections in simplified format", description = "Get all collections in simplified format")
    public ResponseEntity<?> getAll() {
        // This is a placeholder implementation
        return ResponseEntity.ok(new Object[] {
                Map.of(
                        "id", 1,
                        "name", "Clients",
                        "description", "Client collection",
                        "itemCount", 5),
                Map.of(
                        "id", 2,
                        "name", "Projects",
                        "description", "Project collection",
                        "itemCount", 10),
                Map.of(
                        "id", 3,
                        "name", "Invoices",
                        "description", "Invoice collection",
                        "itemCount", 15)
        });
    }
}
