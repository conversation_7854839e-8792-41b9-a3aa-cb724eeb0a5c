package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "invoice_audit_log")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InvoiceAuditLog extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "invoice_id")
    private Invoice invoice;

    @Column(name = "action")
    private String action;

    @Column(name = "performed_by")
    private String performedBy;

    // Add missing fields
    @Column(name = "action_by")
    private String actionBy;

    @Column(name = "action_date")
    private java.time.LocalDateTime actionDate;
}
