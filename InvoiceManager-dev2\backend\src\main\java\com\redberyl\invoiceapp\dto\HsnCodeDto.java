package com.redberyl.invoiceapp.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class HsnCodeDto extends BaseDto {
    private Long id;

    @NotBlank(message = "HSN code is required")
    @Size(min = 6, max = 6, message = "HSN code must be exactly 6 digits")
    @Pattern(regexp = "^[0-9]{6}$", message = "HSN code must contain only 6 digits (0-9)")
    private String code;

    private String description;
    private BigDecimal gstRate;
}
