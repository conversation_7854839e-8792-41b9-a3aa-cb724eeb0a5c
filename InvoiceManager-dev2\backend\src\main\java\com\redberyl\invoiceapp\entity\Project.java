package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "projects")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Project extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_id")
    private Client client;

    @Column(name = "name", nullable = false)
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hsn_code_id")
    private HsnCode hsnCode;

    @Column(name = "description")
    private String description;

    @Column(name = "email")
    private String email;

    @Column(name = "phone")
    private String phone;

    @Column(name = "gst_number")
    private String gstNumber;

    @Column(name = "billing_address")
    private String billingAddress;

    @Column(name = "shipping_address")
    private String shippingAddress;

    @Column(name = "state")
    private String state;

    @Column(name = "engagement_code")
    private String engagementCode;

    @Column(name = "client_partner_name")
    private String clientPartnerName;

    @Column(name = "client_partner_email")
    private String clientPartnerEmail;

    @Column(name = "client_partner_phone")
    private String clientPartnerPhone;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bdm_id")
    private Bdm bdm;

    @Column(name = "commission_percentage", precision = 5, scale = 2)
    private BigDecimal commissionPercentage;

    @Column(name = "commission_amount", precision = 10, scale = 2)
    private BigDecimal commissionAmount;

    @Column(name = "start_date")
    private LocalDate startDate;

    @Column(name = "end_date")
    private LocalDate endDate;

    @Column(name = "status")
    private String status;

    @Column(name = "value", precision = 12, scale = 2)
    private BigDecimal value;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "manager_spoc_id")
    private Spoc managerSpoc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_head_spoc_id")
    private Spoc accountHeadSpoc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "business_head_spoc_id")
    private Spoc businessHeadSpoc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hr_spoc_id")
    private Spoc hrSpoc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "finance_spoc_id")
    private Spoc financeSpoc;

    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private Set<Candidate> candidates = new HashSet<>();

    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private Set<Invoice> invoices = new HashSet<>();
}
