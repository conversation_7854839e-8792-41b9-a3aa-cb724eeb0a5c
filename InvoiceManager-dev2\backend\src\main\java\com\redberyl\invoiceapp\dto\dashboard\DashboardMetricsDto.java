package com.redberyl.invoiceapp.dto.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DashboardMetricsDto {
    private InvoiceMetricsDto totalInvoices;
    private PaymentMetricsDto totalPayments;
    private ClientMetricsDto activeClients;
    private DocumentMetricsDto documents;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class InvoiceMetricsDto {
        private int count;
        private int trend;
        private int pendingApproval;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PaymentMetricsDto {
        private double amount;
        private int trend;
        private int pendingPayments;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ClientMetricsDto {
        private int count;
        private int trend;
        private int newThisMonth;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DocumentMetricsDto {
        private int count;
        private int trend;
        private int awaitingApproval;
    }
}
