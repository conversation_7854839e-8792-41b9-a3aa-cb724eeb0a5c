package com.redberyl.invoiceapp.service;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Simple test to verify HSN code validation logic without any mocking
 */
public class SimpleHsnCodeTest {

    @Test
    void testHsnCodeValidation() {
        // Test valid HSN codes
        assertTrue("998313".matches("^[0-9]{6}$"));
        assertTrue("000001".matches("^[0-9]{6}$"));
        assertTrue("123456".matches("^[0-9]{6}$"));
        
        // Test invalid HSN codes
        assertFalse("9983".matches("^[0-9]{6}$"));    // Too short
        assertFalse("9983134".matches("^[0-9]{6}$"));  // Too long
        assertFalse("99831a".matches("^[0-9]{6}$"));   // Contains letter
        assertFalse("".matches("^[0-9]{6}$"));         // Empty
    }
}
