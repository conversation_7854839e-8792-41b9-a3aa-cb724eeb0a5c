package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.math.BigDecimal;

@Entity
@Table(name = "hsn_codes")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HsnCode extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code", nullable = false, unique = true, length = 6)
    @Size(min = 6, max = 6, message = "HSN code must be exactly 6 digits")
    @Pattern(regexp = "^[0-9]{6}$", message = "HSN code must contain only 6 digits (0-9)")
    private String code;

    @Column(name = "description")
    private String description;

    @Column(name = "gst_rate", precision = 5, scale = 2)
    private BigDecimal gstRate;
}
