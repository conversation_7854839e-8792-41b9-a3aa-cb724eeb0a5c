/**
 * Direct API Service
 *
 * This service provides direct API calls without using the main API service
 * to bypass any potential issues with the main API service.
 */

// Default headers for all requests
const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// Authentication configuration
const AUTH_CONFIG = {
  username: 'admin',
  password: 'admin123'
};

/**
 * Fetch data from an API endpoint with multiple fallback endpoints
 * @param primaryEndpoint The primary endpoint to try
 * @param fallbackEndpoints Array of fallback endpoints to try if the primary endpoint fails
 * @param options Fetch options
 * @returns Promise with the fetched data
 */
export async function fetchWithFallbacks<T>(
  primaryEndpoint: string,
  fallbackEndpoints: string[] = [],
  options: RequestInit = {}
): Promise<T> {
  // Create basic auth header
  const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);

  // Add default headers
  const headers = {
    ...DEFAULT_HEADERS,
    'Authorization': authHeader,
    ...options.headers
  };

  // All endpoints to try
  const allEndpoints = [primaryEndpoint, ...fallbackEndpoints];

  // Try each endpoint
  for (const endpoint of allEndpoints) {
    try {
      console.log(`DirectApiService: Trying endpoint ${endpoint}`);

      console.log(`DirectApiService: Making fetch request to ${endpoint}`);

      // Create fetch options without credentials to avoid CORS issues
      const fetchOptions = {
        ...options,
        headers,
        // Explicitly set credentials to 'omit' to avoid CORS issues
        credentials: 'omit' as RequestCredentials
      };

      console.log(`DirectApiService: Fetch options:`, fetchOptions);

      const response = await fetch(endpoint, fetchOptions);

      if (!response.ok) {
        console.warn(`DirectApiService: Endpoint ${endpoint} returned status ${response.status}`);
        continue;
      }

      const data = await response.json();
      console.log(`DirectApiService: Successfully fetched data from ${endpoint}`, data);

      // Process the response
      let resultArray = [];

      if (Array.isArray(data)) {
        console.log("Data is an array with length:", data.length);
        resultArray = data;
      } else if (data && typeof data === 'object') {
        // Check for common response formats
        if (Array.isArray(data.data)) {
          console.log("Data has data array with length:", data.data.length);
          resultArray = data.data;
        } else if (Array.isArray(data.content)) {
          console.log("Data has content array with length:", data.content.length);
          resultArray = data.content;
        } else if (data.data && Array.isArray(data.data.content)) {
          console.log("Data has nested content array with length:", data.data.content.length);
          resultArray = data.data.content;
        } else if (data.projects && Array.isArray(data.projects)) {
          console.log("Data has projects array with length:", data.projects.length);
          resultArray = data.projects;
        } else if (data.items && Array.isArray(data.items)) {
          console.log("Data has items array with length:", data.items.length);
          resultArray = data.items;
        } else {
          // If it's an object but not in a recognized format, try to convert it to an array
          console.log("Data is an object but not in a recognized format");
          resultArray = [data];
        }
      } else {
        resultArray = [data];
      }

      // If this is a projects endpoint, map the projects to the expected format
      if (endpoint.includes('projects')) {
        const mappedProjects = resultArray.map(project => {
          // Create a properly formatted project object
          return {
            id: project.id || Math.random().toString(36).substring(2, 9),
            name: project.name || "Unnamed Project",
            client: typeof project.client === 'object' ? project.client :
                   project.clientId ? { id: project.clientId, name: project.clientName || `Client ${project.clientId}` } :
                   { id: 0, name: "Unknown Client" },
            description: project.description || "",
            startDate: project.startDate || new Date().toISOString(),
            endDate: project.endDate || new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(),
            status: project.status || "Not Started",
            value: project.value ? (project.value.toString().startsWith('₹') ? project.value : `₹${project.value}`) : "₹0.00",
            // Preserve other fields
            ...project
          };
        });

        console.log("Mapped projects:", mappedProjects);
        return mappedProjects as T;
      }

      return resultArray as T;
    } catch (error) {
      console.error(`DirectApiService: Error fetching from ${endpoint}:`, error);
    }
  }

  // If all endpoints fail, throw an error
  throw new Error(`Failed to fetch data from any endpoint`);
}

/**
 * Direct API service for making API calls
 */
export const directApiService = {
  /**
   * Get all projects
   * @returns Promise with array of projects
   */
  getAllProjects: async (): Promise<any[]> => {
    // Prioritize the specific endpoint mentioned by the user
    const primaryEndpoint = 'http://localhost:8091/projects/getAll';

    const fallbackEndpoints = [
      'http://localhost:8091/api/projects',
      'http://localhost:8091/api/noauth/getProjects',
      'http://localhost:8091/noauth/projects'
    ];

    console.log("DirectApiService: Fetching projects from primary endpoint:", primaryEndpoint);

    return fetchWithFallbacks<any[]>(primaryEndpoint, fallbackEndpoints, {
      method: 'GET'
    });
  },

  /**
   * Create a new project
   * @param projectData Project data to create
   * @returns Promise with created project
   */
  createProject: async (projectData: any): Promise<any> => {
    const endpoints = [
      'http://localhost:8091/api/projects',
      'http://localhost:8091/api/projects/create',
      'http://localhost:8091/api/noauth/createProject',
      'http://localhost:8091/projects/create'
    ];

    return fetchWithFallbacks<any>(endpoints[0], endpoints.slice(1), {
      method: 'POST',
      body: JSON.stringify(projectData)
    });
  },

  /**
   * Get all projects directly from the specified endpoint
   * This is a direct call to the specific endpoint without fallbacks
   * @returns Promise with array of projects
   */
  getProjectsFromSpecificEndpoint: async (): Promise<any[]> => {
    const endpoint = 'http://localhost:8091/projects/getAll';

    console.log(`DirectApiService: Making direct call to specific endpoint: ${endpoint}`);

    try {
      // Create basic auth header
      const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);

      // Create headers
      const headers = {
        ...DEFAULT_HEADERS,
        'Authorization': authHeader
      };

      // Make the request
      const response = await fetch(endpoint, {
        method: 'GET',
        headers,
        credentials: 'omit'
      });

      if (!response.ok) {
        console.error(`DirectApiService: Endpoint ${endpoint} returned status ${response.status}`);
        throw new Error(`Failed to fetch projects from ${endpoint}: ${response.status}`);
      }

      const data = await response.json();
      console.log(`DirectApiService: Successfully fetched projects from specific endpoint:`, data);

      // Process the response
      let projectsArray = [];

      if (Array.isArray(data)) {
        console.log("Data is an array with length:", data.length);
        projectsArray = data;
      } else if (data && typeof data === 'object') {
        // Check for common response formats
        if (Array.isArray(data.data)) {
          console.log("Data has data array with length:", data.data.length);
          projectsArray = data.data;
        } else if (Array.isArray(data.content)) {
          console.log("Data has content array with length:", data.content.length);
          projectsArray = data.content;
        } else if (data.data && Array.isArray(data.data.content)) {
          console.log("Data has nested content array with length:", data.data.content.length);
          projectsArray = data.data.content;
        } else if (data.projects && Array.isArray(data.projects)) {
          console.log("Data has projects array with length:", data.projects.length);
          projectsArray = data.projects;
        } else if (data.items && Array.isArray(data.items)) {
          console.log("Data has items array with length:", data.items.length);
          projectsArray = data.items;
        } else {
          // If it's an object but not in a recognized format, try to convert it to an array
          console.log("Data is an object but not in a recognized format");
          const singleProject = data;
          projectsArray = [singleProject];
        }
      }

      // Map the projects to the expected format
      const mappedProjects = projectsArray.map(project => {
        // Create a properly formatted project object
        return {
          id: project.id || Math.random().toString(36).substring(2, 9),
          name: project.name || "Unnamed Project",
          client: typeof project.client === 'object' ? project.client :
                 project.clientId ? { id: project.clientId, name: project.clientName || `Client ${project.clientId}` } :
                 { id: 0, name: "Unknown Client" },
          description: project.description || "",
          startDate: project.startDate || new Date().toISOString(),
          endDate: project.endDate || new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(),
          status: project.status || "Not Started",
          value: project.value ? (project.value.toString().startsWith('$') ? project.value : `$${project.value}`) : "$0.00",
          // Preserve other fields
          ...project
        };
      });

      console.log("Mapped projects:", mappedProjects);

      return mappedProjects;
    } catch (error) {
      console.error(`DirectApiService: Error fetching from specific endpoint:`, error);
      throw error;
    }
  }
};
