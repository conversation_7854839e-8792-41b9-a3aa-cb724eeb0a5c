import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download, Edit, FileClock, Trash } from "lucide-react";
import { toast } from "sonner";

interface DocumentActionButtonsProps {
  document: any;
  onDownload?: (document: any) => void;
  onEdit?: (document: any) => void;
  onViewHistory?: (document: any) => void;
  onDelete?: (documentId: string) => void;
}

const DocumentActionButtons: React.FC<DocumentActionButtonsProps> = ({
  document,
  onDownload,
  onEdit,
  onViewHistory,
  onDelete,
}) => {
  const handleDownload = () => {
    if (onDownload) {
      onDownload(document);
    } else {
      toast.success(`Downloading ${document.name}`, {
        description: `File type: ${document.type}`,
      });
    }
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(document);
    } else {
      toast.info(`Editing ${document.name}`, {
        description: "Edit functionality would open a form in a real app",
      });
    }
  };

  const handleViewHistory = () => {
    if (onViewHistory) {
      onViewHistory(document);
    } else {
      toast.info(`Viewing version history for ${document.name}`, {
        description: "Version history would be displayed in a real app",
      });
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(document.id);
    } else {
      toast.success(`Deleted ${document.name}`, {
        description: "Document deleted successfully",
      });
    }
  };

  return (
    <div className="flex flex-col gap-2">
      <Button
        variant="ghost"
        size="sm"
        className="flex items-center justify-start px-2 py-1 h-auto hover:bg-gray-100"
        onClick={handleDownload}
      >
        <Download className="mr-2 h-4 w-4" />
        <span>Download</span>
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        className="flex items-center justify-start px-2 py-1 h-auto hover:bg-gray-100"
        onClick={handleEdit}
      >
        <Edit className="mr-2 h-4 w-4" />
        <span>Edit Variables</span>
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        className="flex items-center justify-start px-2 py-1 h-auto hover:bg-gray-100"
        onClick={handleViewHistory}
      >
        <FileClock className="mr-2 h-4 w-4" />
        <span>Version History</span>
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        className="flex items-center justify-start px-2 py-1 h-auto text-red-600 hover:bg-red-50"
        onClick={handleDelete}
      >
        <Trash className="mr-2 h-4 w-4" />
        <span>Delete</span>
      </Button>
    </div>
  );
};

export default DocumentActionButtons;
