package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.GstChallanDto;
import org.springframework.core.io.Resource;

import java.time.LocalDate;
import java.util.List;

public interface GstChallanService {
    
    /**
     * Generate GST Challan data for a specific month
     * @param month The month for which to generate the challan (e.g., "January 2025")
     * @return GstChallanDto containing all invoice data for the month
     */
    GstChallanDto generateGstChallanData(String month);
    
    /**
     * Generate GST Challan data for a date range
     * @param startDate Start date for the challan
     * @param endDate End date for the challan
     * @return GstChallanDto containing all invoice data for the date range
     */
    GstChallanDto generateGstChallanDataForDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * Generate GST Challan data for specific invoice IDs
     * @param invoiceIds List of invoice IDs to include in the challan
     * @return GstChallanDto containing data for the specified invoices only
     */
    GstChallanDto generateGstChallanDataForInvoices(List<Long> invoiceIds);
    
    /**
     * Generate PDF for GST Challan
     * @param gstChallanDto The GST challan data
     * @return PDF resource
     */
    Resource generateGstChallanPdf(GstChallanDto gstChallanDto);
    
    /**
     * Generate GST Challan PDF for a specific month
     * @param month The month for which to generate the challan PDF
     * @return PDF resource
     */
    Resource generateGstChallanPdfForMonth(String month);
}
