/**
 * Utility functions for handling IP addresses and API URLs
 */

/**
 * Get the base API URL based on the current environment
 * This will use the environment variable VITE_API_URL if available, otherwise use current hostname
 * @returns The base API URL
 */
export const getApiBaseUrl = (): string => {
  // First, check if we have VITE_API_URL environment variable
  if (import.meta.env.VITE_API_URL) {
    // Remove /api suffix if present to get base URL
    return import.meta.env.VITE_API_URL.replace('/api', '');
  }

  // Fallback to dynamic hostname detection
  const hostname = window.location.hostname;

  // For development and testing, always use localhost for consistency
  // This ensures the frontend and backend communicate properly
  if (process.env.NODE_ENV === 'development' && (hostname === 'localhost' || hostname === '127.0.0.1')) {
    // When running locally, use localhost for consistency
    return 'http://localhost:8091';
  }

  // For production or when accessed via IP, use the current hostname
  // This allows the app to work with both localhost and IP addresses
  return `http://${hostname}:8091`;
};

/**
 * Get the API URL with the specified endpoint
 * @param endpoint The API endpoint
 * @returns The full API URL
 */
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();

  // Ensure endpoint starts with a slash
  const formattedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

  return `${baseUrl}${formattedEndpoint}`;
};

/**
 * Get the target IP from environment variable or fallback to current hostname
 * @returns The target IP address
 */
export const getTargetIP = (): string => {
  // First, check if we have VITE_API_URL environment variable
  if (import.meta.env.VITE_API_URL) {
    // Extract IP from URL like "http://************:8091/api"
    try {
      const url = new URL(import.meta.env.VITE_API_URL.replace('/api', ''));
      return url.hostname;
    } catch (error) {
      console.warn('Failed to parse VITE_API_URL:', error);
    }
  }

  // Fallback to current hostname
  return window.location.hostname === 'localhost' ? 'localhost' : window.location.hostname;
};

/**
 * Get the backend port from environment variable or fallback to default
 * @returns The backend port number
 */
export const getBackendPort = (): string => {
  // First, check if we have VITE_API_URL environment variable
  if (import.meta.env.VITE_API_URL) {
    // Extract port from URL like "http://************:8091/api"
    try {
      const url = new URL(import.meta.env.VITE_API_URL.replace('/api', ''));
      return url.port || '8091';
    } catch (error) {
      console.warn('Failed to parse VITE_API_URL:', error);
    }
  }

  // Fallback to default port
  return '8091';
};

/**
 * Check if the current hostname is localhost or our specific IP
 * @returns True if the current hostname is localhost, 127.0.0.1, or ************
 */
export const isLocalhost = (): boolean => {
  const hostname = window.location.hostname;
  return hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '************';
};
