package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.config.OneDriveConfig;
import com.redberyl.invoiceapp.dto.OneDriveUploadResponse;
import com.redberyl.invoiceapp.service.OneDriveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.HashMap;

/**
 * Implementation of OneDrive service using Microsoft Graph API
 */
@Service
public class OneDriveServiceImpl implements OneDriveService {

    private static final Logger logger = LoggerFactory.getLogger(OneDriveServiceImpl.class);
    
    @Autowired
    private OneDriveConfig oneDriveConfig;
    
    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public String getAuthorizationUrl() {
        try {
            String state = java.util.UUID.randomUUID().toString();

            // Use native client redirect URI which is pre-configured in most Azure apps
            return "https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/authorize" +
                    "?client_id=" + java.net.URLEncoder.encode(oneDriveConfig.getClientId(), java.nio.charset.StandardCharsets.UTF_8) +
                    "&response_type=code" +
                    "&redirect_uri=" + java.net.URLEncoder.encode(oneDriveConfig.getRedirectUri(), java.nio.charset.StandardCharsets.UTF_8) +
                    "&scope=" + java.net.URLEncoder.encode(oneDriveConfig.getScope(), java.nio.charset.StandardCharsets.UTF_8) +
                    "&state=" + state +
                    "&response_mode=query" +
                    "&prompt=select_account";
        } catch (Exception e) {
            logger.error("Error generating authorization URL", e);
            throw new RuntimeException("Failed to generate authorization URL", e);
        }
    }

    @Override
    public String handleCallback(String code, String state) {
        try {
            String tokenUrl = "https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/token";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("client_id", oneDriveConfig.getClientId());
            body.add("client_secret", oneDriveConfig.getClientSecret());
            body.add("code", code);
            body.add("redirect_uri", oneDriveConfig.getRedirectUri());
            body.add("grant_type", "authorization_code");
            body.add("scope", oneDriveConfig.getScope());
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> tokenResponse = response.getBody();
                return (String) tokenResponse.get("access_token");
            } else {
                throw new RuntimeException("Failed to get access token");
            }
        } catch (Exception e) {
            logger.error("Error handling OAuth callback", e);
            throw new RuntimeException("Failed to handle OAuth callback", e);
        }
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public OneDriveUploadResponse uploadFile(String accessToken, byte[] fileContent, String fileName, String folderPath) {
        try {
            // Create the upload URL - using the correct Microsoft Graph API format
            String uploadUrl = oneDriveConfig.getGraphEndpoint() + "/me/drive/root:" + folderPath + "/" + fileName + ":/content";

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentLength(fileContent.length);

            HttpEntity<byte[]> request = new HttpEntity<>(fileContent, headers);

            ResponseEntity<Map> response = restTemplate.exchange(uploadUrl, HttpMethod.PUT, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                Map<String, Object> responseBody = response.getBody();
                if (responseBody != null) {
                    String fileId = (String) responseBody.get("id");
                    String webUrl = (String) responseBody.get("webUrl");

                    // Handle download URL safely
                    String downloadUrl = null;
                    if (responseBody.containsKey("@microsoft.graph.downloadUrl")) {
                        downloadUrl = (String) responseBody.get("@microsoft.graph.downloadUrl");
                    }

                    Long fileSize = 0L;
                    if (responseBody.containsKey("size")) {
                        fileSize = ((Number) responseBody.get("size")).longValue();
                    }

                    return OneDriveUploadResponse.success(fileId, fileName, webUrl, downloadUrl, fileSize);
                }
            }

            return OneDriveUploadResponse.error("Upload failed with status: " + response.getStatusCode());
        } catch (HttpClientErrorException e) {
            logger.error("HTTP error uploading file to OneDrive: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            return OneDriveUploadResponse.error("Upload failed: " + e.getStatusCode() + " - " + e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.error("Error uploading file to OneDrive", e);
            return OneDriveUploadResponse.error("Upload failed: " + e.getMessage());
        }
    }

    @Override
    public OneDriveUploadResponse uploadInvoicePdf(String accessToken, byte[] pdfContent, String invoiceNumber) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
            String fileName = String.format("Invoice_%s_%s.pdf", invoiceNumber, timestamp);
            String folderPath = oneDriveConfig.getBasePath() + "/Invoices";
            
            return uploadFile(accessToken, pdfContent, fileName, folderPath);
        } catch (Exception e) {
            logger.error("Error uploading invoice PDF to OneDrive", e);
            return OneDriveUploadResponse.error("Failed to upload invoice PDF: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public boolean isAuthenticated(String accessToken) {
        try {
            if (accessToken == null || accessToken.trim().isEmpty()) {
                return false;
            }
            
            String testUrl = oneDriveConfig.getGraphEndpoint() + "/me";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            
            HttpEntity<String> request = new HttpEntity<>(headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(testUrl, HttpMethod.GET, request, Map.class);
            
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            logger.debug("Token validation failed", e);
            return false;
        }
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public String refreshAccessToken(String refreshToken) {
        try {
            String tokenUrl = "https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/token";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("client_id", oneDriveConfig.getClientId());
            body.add("client_secret", oneDriveConfig.getClientSecret());
            body.add("refresh_token", refreshToken);
            body.add("grant_type", "refresh_token");
            body.add("scope", oneDriveConfig.getScope());
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> tokenResponse = response.getBody();
                return (String) tokenResponse.get("access_token");
            } else {
                throw new RuntimeException("Failed to refresh access token");
            }
        } catch (Exception e) {
            logger.error("Error refreshing access token", e);
            throw new RuntimeException("Failed to refresh access token", e);
        }
    }

    @Override
    public Map<String, Object> startDeviceCodeFlow() {
        try {
            // Try common tenant first, then specific tenant
            String deviceCodeUrl = "https://login.microsoftonline.com/common/oauth2/v2.0/devicecode";

            logger.info("Starting device code flow with URL: {}", deviceCodeUrl);
            logger.info("Client ID: {}", oneDriveConfig.getClientId());
            logger.info("Scope: {}", oneDriveConfig.getScope());

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("client_id", oneDriveConfig.getClientId());
            body.add("scope", oneDriveConfig.getScope());

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);

            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.postForEntity(deviceCodeUrl, request, Map.class);

            logger.info("Device code response status: {}", response.getStatusCode());

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> deviceCodeResponse = response.getBody();

                logger.info("Device code response: {}", deviceCodeResponse.keySet());

                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("device_code", deviceCodeResponse.get("device_code"));
                result.put("user_code", deviceCodeResponse.get("user_code"));
                result.put("verification_uri", deviceCodeResponse.get("verification_uri"));
                result.put("expires_in", deviceCodeResponse.get("expires_in"));
                result.put("interval", deviceCodeResponse.get("interval"));
                result.put("message", deviceCodeResponse.get("message"));

                return result;
            } else {
                logger.error("Failed device code response: {}", response.getBody());
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("error", "Failed to start device code flow");
                return error;
            }
        } catch (HttpClientErrorException e) {
            logger.error("HTTP error during device code flow: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());

            // Try with specific tenant if common tenant fails
            if (e.getStatusCode() == HttpStatus.BAD_REQUEST || e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                try {
                    logger.info("Retrying with specific tenant...");
                    String specificTenantUrl = "https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/devicecode";

                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

                    MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
                    body.add("client_id", oneDriveConfig.getClientId());
                    body.add("scope", oneDriveConfig.getScope());

                    HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);

                    @SuppressWarnings("rawtypes")
                    ResponseEntity<Map> retryResponse = restTemplate.postForEntity(specificTenantUrl, request, Map.class);

                    if (retryResponse.getStatusCode() == HttpStatus.OK && retryResponse.getBody() != null) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> deviceCodeResponse = retryResponse.getBody();

                        Map<String, Object> result = new HashMap<>();
                        result.put("success", true);
                        result.put("device_code", deviceCodeResponse.get("device_code"));
                        result.put("user_code", deviceCodeResponse.get("user_code"));
                        result.put("verification_uri", deviceCodeResponse.get("verification_uri"));
                        result.put("expires_in", deviceCodeResponse.get("expires_in"));
                        result.put("interval", deviceCodeResponse.get("interval"));
                        result.put("message", deviceCodeResponse.get("message"));

                        return result;
                    }
                } catch (Exception retryError) {
                    logger.error("Retry with specific tenant also failed", retryError);
                }
            }

            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", "Device code flow failed: " + e.getResponseBodyAsString());
            return error;
        } catch (Exception e) {
            logger.error("Error starting device code flow", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", "Failed to start device code flow: " + e.getMessage());
            return error;
        }
    }

    @Override
    public Map<String, Object> pollDeviceToken(String deviceCode) {
        String tokenUrl = "https://login.microsoftonline.com/common/oauth2/v2.0/token";

        logger.info("Polling device token from URL: {}", tokenUrl);
        logger.info("Using client ID: {}", oneDriveConfig.getClientId());
        logger.info("Device code (first 10 chars): {}", deviceCode.substring(0, Math.min(10, deviceCode.length())));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // Try with client secret first (for confidential clients)
        boolean useClientSecret = oneDriveConfig.getClientSecret() != null &&
                                !oneDriveConfig.getClientSecret().trim().isEmpty() &&
                                !oneDriveConfig.getClientSecret().equals("your_client_secret_here");

        try {
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("grant_type", "urn:ietf:params:oauth:grant-type:device_code");
            body.add("client_id", oneDriveConfig.getClientId());
            body.add("device_code", deviceCode);

            if (useClientSecret) {
                body.add("client_secret", oneDriveConfig.getClientSecret());
                logger.info("Using confidential client authentication with client secret");
            } else {
                logger.info("Using public client authentication (no client secret)");
            }

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);

            logger.info("Making token request with body: {}", body.toSingleValueMap().keySet());

            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);

            logger.info("Token response status: {}", response.getStatusCode());

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> tokenResponse = response.getBody();

                logger.info("Token response keys: {}", tokenResponse.keySet());

                if (tokenResponse.containsKey("access_token")) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("success", true);
                    result.put("access_token", tokenResponse.get("access_token"));
                    result.put("token_type", tokenResponse.get("token_type"));
                    result.put("expires_in", tokenResponse.get("expires_in"));
                    result.put("scope", tokenResponse.get("scope"));
                    result.put("refresh_token", tokenResponse.get("refresh_token"));

                    logger.info("Successfully obtained access token");
                    return result;
                } else {
                    logger.warn("No access token in response: {}", tokenResponse);
                    Map<String, Object> error = new HashMap<>();
                    error.put("success", false);
                    error.put("error", "No access token in response");
                    return error;
                }
            } else {
                logger.warn("Non-OK response: {} - {}", response.getStatusCode(), response.getBody());
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("error", "Token not ready yet");
                return error;
            }
        } catch (HttpClientErrorException e) {
            logger.error("HTTP error during device token polling: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());

            String responseBody = e.getResponseBodyAsString();

            // If we get unauthorized and we used client secret, try again without it
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED && useClientSecret) {
                logger.warn("Unauthorized with client secret, retrying as public client...");

                try {
                    // Retry without client secret (public client)
                    MultiValueMap<String, String> publicBody = new LinkedMultiValueMap<>();
                    publicBody.add("grant_type", "urn:ietf:params:oauth:grant-type:device_code");
                    publicBody.add("client_id", oneDriveConfig.getClientId());
                    publicBody.add("device_code", deviceCode);

                    HttpEntity<MultiValueMap<String, String>> publicRequest = new HttpEntity<>(publicBody, headers);

                    @SuppressWarnings("rawtypes")
                    ResponseEntity<Map> retryResponse = restTemplate.postForEntity(tokenUrl, publicRequest, Map.class);

                    if (retryResponse.getStatusCode() == HttpStatus.OK && retryResponse.getBody() != null) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> tokenResponse = retryResponse.getBody();

                        if (tokenResponse.containsKey("access_token")) {
                            Map<String, Object> result = new HashMap<>();
                            result.put("success", true);
                            result.put("access_token", tokenResponse.get("access_token"));
                            result.put("token_type", tokenResponse.get("token_type"));
                            result.put("expires_in", tokenResponse.get("expires_in"));
                            result.put("scope", tokenResponse.get("scope"));
                            result.put("refresh_token", tokenResponse.get("refresh_token"));

                            logger.info("Successfully obtained access token using public client");
                            return result;
                        }
                    }
                } catch (Exception retryError) {
                    logger.error("Retry as public client also failed", retryError);
                }
            }

            Map<String, Object> error = new HashMap<>();
            error.put("success", false);

            if (e.getStatusCode() == HttpStatus.BAD_REQUEST) {
                if (responseBody.contains("authorization_pending")) {
                    error.put("error", "authorization_pending");
                    error.put("message", "User has not yet completed authentication");
                } else if (responseBody.contains("authorization_declined")) {
                    error.put("error", "authorization_declined");
                    error.put("message", "User declined the authorization request");
                } else if (responseBody.contains("expired_token")) {
                    error.put("error", "expired_token");
                    error.put("message", "Device code has expired");
                } else {
                    error.put("error", "bad_request");
                    error.put("message", "Bad request: " + responseBody);
                }
            } else if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                error.put("error", "unauthorized");
                error.put("message", "Invalid client credentials. Client secret may be expired or app may need to be configured as public client in Azure Portal.");
                logger.error("401 Unauthorized - Client secret may be expired or invalid. Consider configuring app as public client.");
            } else {
                error.put("error", "http_error");
                error.put("message", "HTTP " + e.getStatusCode() + ": " + responseBody);
            }

            return error;
        } catch (Exception e) {
            logger.error("Unexpected error polling device token", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", "unexpected_error");
            error.put("message", "Failed to poll device token: " + e.getMessage());
            return error;
        }
    }
}
