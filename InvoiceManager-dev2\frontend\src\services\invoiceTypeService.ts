import { api } from './api';

export interface InvoiceType {
  id?: number | string;
  name: string;
  description?: string;
}

class InvoiceTypeService {
  private baseUrl = 'http://localhost:8091';

  // Helper method to get auth headers
  private getAuthHeaders(): Record<string, string> {
    // For basic auth (used in development)
    return {
      'Authorization': 'Basic ' + btoa('admin:admin123'),
      'Content-Type': 'application/json'
    };
  }

  /**
   * Get all invoice types
   * @returns Promise with array of invoice types
   */
  async getAllInvoiceTypes(): Promise<InvoiceType[]> {
    console.log('InvoiceTypeService: Fetching all invoice types');

    try {
      // Try using the api utility first
      return await api.getInvoiceTypes();
    } catch (error) {
      console.error('InvoiceTypeService: Error fetching invoice types via api utility:', error);

      // If API method fails, try direct fetch
      console.log('InvoiceTypeService: Trying alternative approach to fetch invoice types');

      // Try multiple endpoints
      const endpoints = [
        `${this.baseUrl}/invoice-types/getAll`,
        `${this.baseUrl}/api/invoice-types`,
        `${this.baseUrl}/api/v1/invoice-types`,
        `${this.baseUrl}/invoice-types`,
        `${this.baseUrl}/api/noauth/invoice-types`,
        `${this.baseUrl}/api/noauth/invoice-types/test`,
        `http://localhost:8091/api/noauth/invoice-types`,
        `http://localhost:8091/api/noauth/invoice-types/test`
      ];

      for (const endpoint of endpoints) {
        try {
          console.log(`InvoiceTypeService: Trying endpoint: ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: this.getAuthHeaders()
          });

          if (!response.ok) {
            console.warn(`InvoiceTypeService: Endpoint ${endpoint} returned status ${response.status}`);
            continue;
          }

          const data = await response.json();
          console.log(`InvoiceTypeService: Successfully fetched invoice types from ${endpoint}:`, data);
          return data;
        } catch (endpointError) {
          console.warn(`InvoiceTypeService: Error fetching from ${endpoint}:`, endpointError);
        }
      }

      // If all endpoints fail, throw the original error
      throw error;
    }
  }

  /**
   * Get invoice type by ID
   * @param id Invoice type ID
   * @returns Promise with invoice type
   */
  async getInvoiceTypeById(id: number | string): Promise<InvoiceType> {
    console.log(`InvoiceTypeService: Fetching invoice type with ID ${id}`);

    try {
      // Try using the api utility first
      return await api.getInvoiceType(Number(id));
    } catch (error) {
      console.error(`InvoiceTypeService: Error fetching invoice type with ID ${id} via api utility:`, error);

      // If API method fails, try direct fetch
      console.log('InvoiceTypeService: Trying alternative approach to fetch invoice type');

      // Try multiple endpoints
      const endpoints = [
        `${this.baseUrl}/invoice-types/getById/${id}`,
        `${this.baseUrl}/api/invoice-types/${id}`,
        `${this.baseUrl}/api/v1/invoice-types/${id}`
      ];

      for (const endpoint of endpoints) {
        try {
          console.log(`InvoiceTypeService: Trying endpoint: ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: this.getAuthHeaders()
          });

          if (!response.ok) {
            console.warn(`InvoiceTypeService: Endpoint ${endpoint} returned status ${response.status}`);
            continue;
          }

          const data = await response.json();
          console.log(`InvoiceTypeService: Successfully fetched invoice type from ${endpoint}:`, data);
          return data;
        } catch (endpointError) {
          console.warn(`InvoiceTypeService: Error fetching from ${endpoint}:`, endpointError);
        }
      }

      // If all endpoints fail, throw the original error
      throw error;
    }
  }

  /**
   * Create a new invoice type
   * @param invoiceType Invoice type data
   * @returns Promise with created invoice type
   */
  async createInvoiceType(invoiceType: InvoiceType): Promise<InvoiceType> {
    console.log('InvoiceTypeService: Creating invoice type:', invoiceType);

    // Try multiple endpoints
    const endpoints = [
      `${this.baseUrl}/invoice-types/create`,
      `${this.baseUrl}/api/invoice-types`,
      `${this.baseUrl}/api/v1/invoice-types`
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`InvoiceTypeService: Trying endpoint: ${endpoint}`);
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(invoiceType)
        });

        if (!response.ok) {
          console.warn(`InvoiceTypeService: Endpoint ${endpoint} returned status ${response.status}`);
          continue;
        }

        const data = await response.json();
        console.log(`InvoiceTypeService: Successfully created invoice type from ${endpoint}:`, data);
        return data;
      } catch (endpointError) {
        console.warn(`InvoiceTypeService: Error creating from ${endpoint}:`, endpointError);
      }
    }

    throw new Error('Failed to create invoice type');
  }

  /**
   * Update an existing invoice type
   * @param id Invoice type ID
   * @param invoiceType Invoice type data
   * @returns Promise with updated invoice type
   */
  async updateInvoiceType(id: number | string, invoiceType: InvoiceType): Promise<InvoiceType> {
    console.log(`InvoiceTypeService: Updating invoice type with ID ${id}:`, invoiceType);

    // Try multiple endpoints
    const endpoints = [
      `${this.baseUrl}/invoice-types/update/${id}`,
      `${this.baseUrl}/api/invoice-types/${id}`,
      `${this.baseUrl}/api/v1/invoice-types/${id}`
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`InvoiceTypeService: Trying endpoint: ${endpoint}`);
        const response = await fetch(endpoint, {
          method: 'PUT',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(invoiceType)
        });

        if (!response.ok) {
          console.warn(`InvoiceTypeService: Endpoint ${endpoint} returned status ${response.status}`);
          continue;
        }

        const data = await response.json();
        console.log(`InvoiceTypeService: Successfully updated invoice type from ${endpoint}:`, data);
        return data;
      } catch (endpointError) {
        console.warn(`InvoiceTypeService: Error updating from ${endpoint}:`, endpointError);
      }
    }

    throw new Error(`Failed to update invoice type with ID ${id}`);
  }

  /**
   * Delete an invoice type
   * @param id Invoice type ID
   * @returns Promise with void
   */
  async deleteInvoiceType(id: number | string): Promise<void> {
    console.log(`InvoiceTypeService: Deleting invoice type with ID ${id}`);

    // Try multiple endpoints
    const endpoints = [
      `${this.baseUrl}/invoice-types/deleteById/${id}`,
      `${this.baseUrl}/api/invoice-types/${id}`,
      `${this.baseUrl}/api/v1/invoice-types/${id}`
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`InvoiceTypeService: Trying endpoint: ${endpoint}`);
        const response = await fetch(endpoint, {
          method: 'DELETE',
          headers: this.getAuthHeaders()
        });

        if (!response.ok) {
          console.warn(`InvoiceTypeService: Endpoint ${endpoint} returned status ${response.status}`);
          continue;
        }

        console.log(`InvoiceTypeService: Successfully deleted invoice type from ${endpoint}`);
        return;
      } catch (endpointError) {
        console.warn(`InvoiceTypeService: Error deleting from ${endpoint}:`, endpointError);
      }
    }

    throw new Error(`Failed to delete invoice type with ID ${id}`);
  }
}

export const invoiceTypeService = new InvoiceTypeService();
