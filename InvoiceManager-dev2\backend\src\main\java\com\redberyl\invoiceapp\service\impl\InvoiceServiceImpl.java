package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.*;
import com.redberyl.invoiceapp.entity.*;
import com.redberyl.invoiceapp.enums.InvoiceStatus;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.*;
import com.redberyl.invoiceapp.service.InvoiceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class InvoiceServiceImpl implements InvoiceService {

    private static final Logger log = LoggerFactory.getLogger(InvoiceServiceImpl.class);

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private InvoiceTypeRepository invoiceTypeRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private CandidateRepository candidateRepository;

    @Autowired
    private StaffingTypeRepository staffingTypeRepository;

    @Autowired
    private HsnCodeRepository hsnCodeRepository;

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @Autowired
    private InvoiceAuditLogRepository invoiceAuditLogRepository;

    @Override
    public List<InvoiceDto> getAllInvoices() {
        // Use the method that loads all relations including BDM data
        List<Invoice> invoices = invoiceRepository.findAllWithAllRelations();
        if (invoices.isEmpty()) {
            throw new NoContentException("No invoices found");
        }
        return invoices.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public InvoiceDto getInvoiceById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice ID cannot be null");
        }

        // Use the method that loads all relations including BDM data
        Invoice invoice = invoiceRepository.findByIdWithAllRelations(id)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + id));
        return convertToDto(invoice);
    }

    @Override
    public InvoiceDto getInvoiceByNumber(String invoiceNumber) {
        if (!StringUtils.hasText(invoiceNumber)) {
            throw new NullConstraintViolationException("invoiceNumber", "Invoice number cannot be empty");
        }

        // Use the method that loads all relations including BDM data
        Invoice invoice = invoiceRepository.findByInvoiceNumberWithAllRelations(invoiceNumber)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with number: " + invoiceNumber));
        return convertToDto(invoice);
    }

    @Override
    public List<InvoiceDto> getInvoicesByClientId(Long clientId) {
        if (clientId == null) {
            throw new NullConstraintViolationException("clientId", "Client ID cannot be null");
        }

        // Check if client exists
        if (!clientRepository.existsById(clientId)) {
            throw new ResourceNotFoundException("Client not found with id: " + clientId);
        }

        List<Invoice> invoices = invoiceRepository.findByClientId(clientId);
        if (invoices.isEmpty()) {
            throw new NoContentException("No invoices found for client with id: " + clientId);
        }

        return invoices.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getInvoicesByProjectId(Long projectId) {
        if (projectId == null) {
            throw new NullConstraintViolationException("projectId", "Project ID cannot be null");
        }

        // Check if project exists
        if (!projectRepository.existsById(projectId)) {
            throw new ResourceNotFoundException("Project not found with id: " + projectId);
        }

        List<Invoice> invoices = invoiceRepository.findByProjectId(projectId);
        if (invoices.isEmpty()) {
            throw new NoContentException("No invoices found for project with id: " + projectId);
        }

        return invoices.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getInvoicesByCandidateId(Long candidateId) {
        if (candidateId == null) {
            throw new NullConstraintViolationException("candidateId", "Candidate ID cannot be null");
        }

        // Check if candidate exists
        if (!candidateRepository.existsById(candidateId)) {
            throw new ResourceNotFoundException("Candidate not found with id: " + candidateId);
        }

        List<Invoice> invoices = invoiceRepository.findByCandidateId(candidateId);
        if (invoices.isEmpty()) {
            throw new NoContentException("No invoices found for candidate with id: " + candidateId);
        }

        return invoices.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getInvoicesByDateRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null) {
            throw new NullConstraintViolationException("startDate", "Start date cannot be null");
        }

        if (endDate == null) {
            throw new NullConstraintViolationException("endDate", "End date cannot be null");
        }

        if (startDate.isAfter(endDate)) {
            throw new CustomException("Start date cannot be after end date", null);
        }

        List<Invoice> invoices = invoiceRepository.findByInvoiceDateBetween(startDate, endDate);
        if (invoices.isEmpty()) {
            throw new NoContentException("No invoices found between " + startDate + " and " + endDate);
        }

        return invoices.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getOverdueInvoices(LocalDate currentDate) {
        if (currentDate == null) {
            throw new NullConstraintViolationException("currentDate", "Current date cannot be null");
        }

        List<Invoice> invoices = invoiceRepository.findByDueDateBefore(currentDate);
        if (invoices.isEmpty()) {
            throw new NoContentException("No overdue invoices found as of " + currentDate);
        }

        return invoices.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getRecurringInvoices(Boolean isRecurring) {
        if (isRecurring == null) {
            throw new NullConstraintViolationException("isRecurring", "isRecurring flag cannot be null");
        }

        List<Invoice> invoices = invoiceRepository.findByIsRecurring(isRecurring);
        if (invoices.isEmpty()) {
            throw new NoContentException("No " + (isRecurring ? "recurring" : "non-recurring") + " invoices found");
        }

        return invoices.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getInvoicesByStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            throw new NullConstraintViolationException("status", "Status cannot be null or empty");
        }

        try {
            // Try to parse as InvoiceStatus enum
            InvoiceStatus invoiceStatus = InvoiceStatus.valueOf(status.toUpperCase());
            List<Invoice> invoices = invoiceRepository.findByStatus(invoiceStatus);

            if (invoices.isEmpty()) {
                throw new NoContentException("No invoices found with status: " + status);
            }

            return invoices.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid status value: " + status + ". Valid values are: " +
                    Arrays.toString(InvoiceStatus.values()));
        }
    }

    private void validateInvoiceDto(InvoiceDto invoiceDto) {
        if (invoiceDto == null) {
            throw new NullConstraintViolationException("invoiceDto", "Invoice data cannot be null");
        }

        if (!StringUtils.hasText(invoiceDto.getInvoiceNumber())) {
            throw new NullConstraintViolationException("invoiceNumber", "Invoice number cannot be empty");
        }

        // Check for duplicate invoice number if it's a new invoice
        if (invoiceDto.getId() == null &&
                invoiceRepository.findByInvoiceNumber(invoiceDto.getInvoiceNumber()).isPresent()) {
            throw new UniqueConstraintViolationException("invoiceNumber",
                    "Invoice with number " + invoiceDto.getInvoiceNumber() + " already exists");
        }

        if (invoiceDto.getClientId() == null) {
            throw new NullConstraintViolationException("clientId", "Client ID cannot be null");
        }

        if (!clientRepository.existsById(invoiceDto.getClientId())) {
            throw new ForeignKeyViolationException("clientId",
                    "Client not found with id: " + invoiceDto.getClientId());
        }

        if (invoiceDto.getInvoiceTypeId() == null) {
            throw new NullConstraintViolationException("invoiceTypeId", "Invoice type ID cannot be null");
        }

        if (!invoiceTypeRepository.existsById(invoiceDto.getInvoiceTypeId())) {
            throw new ForeignKeyViolationException("invoiceTypeId",
                    "Invoice type not found with id: " + invoiceDto.getInvoiceTypeId());
        }

        if (invoiceDto.getInvoiceDate() == null) {
            throw new NullConstraintViolationException("invoiceDate", "Invoice date cannot be null");
        }

        if (invoiceDto.getDueDate() == null) {
            throw new NullConstraintViolationException("dueDate", "Due date cannot be null");
        }

        if (invoiceDto.getBillingAmount() == null) {
            throw new NullConstraintViolationException("billingAmount", "Billing amount cannot be null");
        }

        // Validate project if provided
        if (invoiceDto.getProjectId() != null && !projectRepository.existsById(invoiceDto.getProjectId())) {
            throw new ForeignKeyViolationException("projectId",
                    "Project not found with id: " + invoiceDto.getProjectId());
        }

        // Validate candidate if provided
        if (invoiceDto.getCandidateId() != null && !candidateRepository.existsById(invoiceDto.getCandidateId())) {
            throw new ForeignKeyViolationException("candidateId",
                    "Candidate not found with id: " + invoiceDto.getCandidateId());
        }

        // Validate staffing type if provided
        if (invoiceDto.getStaffingTypeId() != null
                && !staffingTypeRepository.existsById(invoiceDto.getStaffingTypeId())) {
            throw new ForeignKeyViolationException("staffingTypeId",
                    "Staffing type not found with id: " + invoiceDto.getStaffingTypeId());
        }

        // Validate HSN code if provided
        if (invoiceDto.getHsnId() != null && !hsnCodeRepository.existsById(invoiceDto.getHsnId())) {
            throw new ForeignKeyViolationException("hsnId",
                    "HSN code not found with id: " + invoiceDto.getHsnId());
        }

        // Validate Redberyl account if provided
        if (invoiceDto.getRedberylAccountId() != null &&
                !redberylAccountRepository.existsById(invoiceDto.getRedberylAccountId())) {
            throw new ForeignKeyViolationException("redberylAccountId",
                    "Redberyl account not found with id: " + invoiceDto.getRedberylAccountId());
        }
    }

    @Override
    @Transactional
    public InvoiceDto createInvoice(InvoiceDto invoiceDto) {
        // Log candidate data before validation
        System.out.println("InvoiceServiceImpl - Before validation - Candidate ID: " + invoiceDto.getCandidateId());
        System.out.println("InvoiceServiceImpl - Before validation - Candidate: " +
            (invoiceDto.getCandidate() != null ? invoiceDto.getCandidate().getName() : "null"));

        validateInvoiceDto(invoiceDto);

        // Log candidate data after validation
        System.out.println("InvoiceServiceImpl - After validation - Candidate ID: " + invoiceDto.getCandidateId());
        System.out.println("InvoiceServiceImpl - After validation - Candidate: " +
            (invoiceDto.getCandidate() != null ? invoiceDto.getCandidate().getName() : "null"));

        try {
            Invoice invoice = convertToEntity(invoiceDto);

            // Special handling for candidate
            if (invoiceDto.getCandidateId() != null) {
                try {
                    System.out.println("InvoiceServiceImpl - Setting candidate with ID: " + invoiceDto.getCandidateId());
                    Candidate candidate = candidateRepository.findById(invoiceDto.getCandidateId())
                            .orElse(null);

                    if (candidate != null) {
                        System.out.println("InvoiceServiceImpl - Found candidate in database: " + candidate.getName());
                        invoice.setCandidate(candidate);
                    } else {
                        System.out.println("InvoiceServiceImpl - Candidate not found in database with ID: " + invoiceDto.getCandidateId());

                        // If we have candidate data in the DTO but not in the database, create a new candidate
                        if (invoiceDto.getCandidate() != null && invoiceDto.getCandidate().getName() != null) {
                            System.out.println("InvoiceServiceImpl - Creating new candidate from DTO: " + invoiceDto.getCandidate().getName());
                            Candidate newCandidate = new Candidate();
                            newCandidate.setId(invoiceDto.getCandidateId());
                            newCandidate.setName(invoiceDto.getCandidate().getName());
                            // No need to set email and phone as they don't exist in the entity

                            try {
                                newCandidate = candidateRepository.save(newCandidate);
                                System.out.println("InvoiceServiceImpl - Created new candidate: " + newCandidate.getName());
                                invoice.setCandidate(newCandidate);
                            } catch (Exception e) {
                                System.err.println("InvoiceServiceImpl - Error creating new candidate: " + e.getMessage());
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("Error setting candidate: {}", e.getMessage());
                    System.err.println("InvoiceServiceImpl - Error setting candidate: " + e.getMessage());
                }
            }

            Invoice savedInvoice = invoiceRepository.save(invoice);

            // Log the saved invoice's candidate
            if (savedInvoice.getCandidate() != null) {
                System.out.println("InvoiceServiceImpl - Saved invoice with candidate: " +
                    savedInvoice.getCandidate().getName() + " (ID: " + savedInvoice.getCandidate().getId() + ")");
            } else {
                System.out.println("InvoiceServiceImpl - Saved invoice without candidate");
            }

            // Create audit log entry
            createAuditLog(savedInvoice, "Invoice created", "System");

            InvoiceDto resultDto = convertToDto(savedInvoice);

            // Log the result DTO's candidate
            System.out.println("InvoiceServiceImpl - Result DTO candidate ID: " + resultDto.getCandidateId());
            System.out.println("InvoiceServiceImpl - Result DTO candidate: " +
                (resultDto.getCandidate() != null ? resultDto.getCandidate().getName() : "null"));

            return resultDto;
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("invoiceNumber", "Invoice number already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating invoice: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating invoice", e);
        }
    }

    @Override
    @Transactional
    public InvoiceDto updateInvoice(Long id, InvoiceDto invoiceDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice ID cannot be null");
        }

        if (invoiceDto == null) {
            throw new NullConstraintViolationException("invoiceDto", "Invoice data cannot be null");
        }

        Invoice existingInvoice = invoiceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + id));

        System.out.println("=== INVOICE UPDATE DEBUG ===");
        System.out.println("Updating invoice ID: " + id);
        System.out.println("Existing invoice: " + existingInvoice.getInvoiceNumber());
        System.out.println("DTO data: " + invoiceDto);
        System.out.println("DTO invoice number: " + invoiceDto.getInvoiceNumber());

        // Check for duplicate invoice number if it's being changed
        if (StringUtils.hasText(invoiceDto.getInvoiceNumber()) &&
                !invoiceDto.getInvoiceNumber().equals(existingInvoice.getInvoiceNumber()) &&
                invoiceRepository.findByInvoiceNumber(invoiceDto.getInvoiceNumber()).isPresent()) {
            System.out.println("Duplicate invoice number detected: " + invoiceDto.getInvoiceNumber());
            throw new UniqueConstraintViolationException("invoiceNumber",
                    "Invoice with number " + invoiceDto.getInvoiceNumber() + " already exists");
        }

        // Validate foreign keys if provided - but be more lenient for updates
        try {
            if (invoiceDto.getClientId() != null && !clientRepository.existsById(invoiceDto.getClientId())) {
                System.out.println("Warning: Client not found with id: " + invoiceDto.getClientId() + ", skipping client update");
                invoiceDto.setClientId(null); // Don't update if invalid
            }

            if (invoiceDto.getInvoiceTypeId() != null && !invoiceTypeRepository.existsById(invoiceDto.getInvoiceTypeId())) {
                System.out.println("Warning: Invoice type not found with id: " + invoiceDto.getInvoiceTypeId() + ", skipping invoice type update");
                invoiceDto.setInvoiceTypeId(null); // Don't update if invalid
            }

            if (invoiceDto.getProjectId() != null && !projectRepository.existsById(invoiceDto.getProjectId())) {
                System.out.println("Warning: Project not found with id: " + invoiceDto.getProjectId() + ", skipping project update");
                invoiceDto.setProjectId(null); // Don't update if invalid
            }

            if (invoiceDto.getCandidateId() != null && !candidateRepository.existsById(invoiceDto.getCandidateId())) {
                System.out.println("Warning: Candidate not found with id: " + invoiceDto.getCandidateId() + ", skipping candidate update");
                invoiceDto.setCandidateId(null); // Don't update if invalid
            }

            if (invoiceDto.getStaffingTypeId() != null
                    && !staffingTypeRepository.existsById(invoiceDto.getStaffingTypeId())) {
                System.out.println("Warning: Staffing type not found with id: " + invoiceDto.getStaffingTypeId() + ", skipping staffing type update");
                invoiceDto.setStaffingTypeId(null); // Don't update if invalid
            }

            if (invoiceDto.getHsnId() != null && !hsnCodeRepository.existsById(invoiceDto.getHsnId())) {
                System.out.println("Warning: HSN code not found with id: " + invoiceDto.getHsnId() + ", skipping HSN code update");
                invoiceDto.setHsnId(null); // Don't update if invalid
            }

            if (invoiceDto.getRedberylAccountId() != null &&
                    !redberylAccountRepository.existsById(invoiceDto.getRedberylAccountId())) {
                System.out.println("Warning: Redberyl account not found with id: " + invoiceDto.getRedberylAccountId() + ", skipping redberyl account update");
                invoiceDto.setRedberylAccountId(null); // Don't update if invalid
            }
        } catch (Exception e) {
            System.out.println("Error during foreign key validation: " + e.getMessage());
            // Continue with update even if validation fails
        }

        try {
            updateInvoiceFromDto(existingInvoice, invoiceDto);

            Invoice updatedInvoice = invoiceRepository.save(existingInvoice);

            // Create audit log entry
            createAuditLog(updatedInvoice, "Invoice updated", "System");

            return convertToDto(updatedInvoice);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("invoiceNumber", "Invoice number already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating invoice: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating invoice", e);
        }
    }

    @Override
    @Transactional
    public InvoiceDto updateInvoiceStatus(Long id, InvoiceStatus status) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice ID cannot be null");
        }

        if (status == null) {
            throw new NullConstraintViolationException("status", "Invoice status cannot be null");
        }

        try {
            Invoice existingInvoice = invoiceRepository.findById(id)
                    .orElseThrow(() -> new ResourceNotFoundException("Invoice", "id", id));

            existingInvoice.setStatus(status);
            Invoice updatedInvoice = invoiceRepository.save(existingInvoice);

            // Create audit log entry
            createAuditLog(updatedInvoice, "Invoice status updated to " + status.getDisplayName(), "System");

            return convertToDto(updatedInvoice);
        } catch (Exception e) {
            throw new CustomException("Error updating invoice status", e);
        }
    }

    @Override
    @Transactional
    public InvoiceDto publishInvoice(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice ID cannot be null");
        }

        Invoice invoice = invoiceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + id));

        if (invoice.getPublishedToFinance() != null && invoice.getPublishedToFinance()) {
            throw new CustomException("Invoice is already published to finance", null);
        }

        try {
            invoice.setPublishedToFinance(true);
            invoice.setPublishedAt(LocalDateTime.now());

            Invoice publishedInvoice = invoiceRepository.save(invoice);

            // Create audit log entry
            createAuditLog(publishedInvoice, "Invoice published to finance", "System");

            return convertToDto(publishedInvoice);
        } catch (Exception e) {
            throw new CustomException("Error publishing invoice: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void deleteInvoice(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice ID cannot be null");
        }

        if (!invoiceRepository.existsById(id)) {
            throw new ResourceNotFoundException("Invoice not found with id: " + id);
        }

        try {
            // Create audit log entry before deletion
            Invoice invoice = invoiceRepository.findById(id).get();

            // Check if invoice is published
            if (invoice.getPublishedToFinance() != null && invoice.getPublishedToFinance()) {
                throw new CustomException("Cannot delete a published invoice", null);
            }

            createAuditLog(invoice, "Invoice deleted", "System");

            invoiceRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete invoice because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting invoice: " + e.getMessage(), e);
            }
        } catch (ResourceNotFoundException | NullConstraintViolationException | CustomException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomException("Error deleting invoice", e);
        }
    }

    private void createAuditLog(Invoice invoice, String action, String actionBy) {
        InvoiceAuditLog auditLog = new InvoiceAuditLog();
        auditLog.setInvoice(invoice);
        auditLog.setAction(action);
        auditLog.setActionBy(actionBy);
        auditLog.setActionDate(LocalDateTime.now());

        invoiceAuditLogRepository.save(auditLog);
    }

    private InvoiceDto convertToDto(Invoice invoice) {
        // Start building the DTO with common fields
        InvoiceDto.InvoiceDtoBuilder builder = InvoiceDto.builder()
                .id(invoice.getId())
                .invoiceNumber(invoice.getInvoiceNumber())
                .rate(invoice.getRate())
                .billingAmount(invoice.getBillingAmount())
                .taxAmount(invoice.getTaxAmount())
                .totalAmount(invoice.getTotalAmount())
                .invoiceDate(invoice.getInvoiceDate())
                .dueDate(invoice.getDueDate())
                .isRecurring(invoice.getIsRecurring())
                .publishedToFinance(invoice.getPublishedToFinance())
                .publishedAt(invoice.getPublishedAt())
                .status(invoice.getStatus());

        // Set client if available
        if (invoice.getClient() != null) {
            builder.clientId(invoice.getClient().getId());

            // Create and set the client DTO with essential fields including GST number
            ClientDto clientDto = ClientDto.builder()
                    .id(invoice.getClient().getId())
                    .name(invoice.getClient().getName())
                    .gstNumber(invoice.getClient().getGstNumber())
                    .billingAddress(invoice.getClient().getBillingAddress())
                    .build();

            // Set audit fields for client
            clientDto.setCreatedAt(invoice.getClient().getCreatedAt());
            clientDto.setUpdatedAt(invoice.getClient().getModifiedAt());

            builder.client(clientDto);
        }

        // Set invoice type if available
        if (invoice.getInvoiceType() != null) {
            builder.invoiceTypeId(invoice.getInvoiceType().getId());

            // Create and set the invoice type DTO
            InvoiceTypeDto invoiceTypeDto = InvoiceTypeDto.builder()
                    .id(invoice.getInvoiceType().getId())
                    .invoiceType(invoice.getInvoiceType().getInvoiceType())
                    .typeDesc(invoice.getInvoiceType().getTypeDesc())
                    .build();

            // Set audit fields for invoice type
            invoiceTypeDto.setCreatedAt(invoice.getInvoiceType().getCreatedAt());
            invoiceTypeDto.setUpdatedAt(invoice.getInvoiceType().getModifiedAt());

            builder.invoiceType(invoiceTypeDto);
        }

        // Set project if available
        if (invoice.getProject() != null) {
            builder.projectId(invoice.getProject().getId());

            // Create and set the project DTO with complete fields including BDM commission data and engagement code
            ProjectDto.ProjectDtoBuilder projectBuilder = ProjectDto.builder()
                    .id(invoice.getProject().getId())
                    .name(invoice.getProject().getName())
                    .description(invoice.getProject().getDescription())
                    .engagementCode(invoice.getProject().getEngagementCode())
                    .gstNumber(invoice.getProject().getGstNumber())
                    .billingAddress(invoice.getProject().getBillingAddress())
                    .commissionPercentage(invoice.getProject().getCommissionPercentage())
                    .commissionAmount(invoice.getProject().getCommissionAmount());

            // Include BDM information if available
            if (invoice.getProject().getBdm() != null) {
                BdmDto bdmDto = BdmDto.builder()
                        .id(invoice.getProject().getBdm().getId())
                        .name(invoice.getProject().getBdm().getName())
                        .commissionRate(invoice.getProject().getBdm().getCommissionRate())
                        .build();
                projectBuilder.bdm(bdmDto);
            }

            ProjectDto projectDto = projectBuilder.build();

            // Set audit fields for project
            projectDto.setCreatedAt(invoice.getProject().getCreatedAt());
            projectDto.setUpdatedAt(invoice.getProject().getModifiedAt());

            builder.project(projectDto);
        }

        // Set candidate if available
        if (invoice.getCandidate() != null) {
            System.out.println("Converting candidate to DTO: " + invoice.getCandidate().getName() + " (ID: " + invoice.getCandidate().getId() + ")");
            System.out.println("Candidate billing rate: " + invoice.getCandidate().getBillingRate());
            builder.candidateId(invoice.getCandidate().getId());

            // Create and set the candidate DTO with essential fields including billing rate
            CandidateDto candidateDto = CandidateDto.builder()
                    .id(invoice.getCandidate().getId())
                    .name(invoice.getCandidate().getName())
                    .billingRate(invoice.getCandidate().getBillingRate())
                    .build();

            // Set audit fields for candidate
            candidateDto.setCreatedAt(invoice.getCandidate().getCreatedAt());
            candidateDto.setUpdatedAt(invoice.getCandidate().getModifiedAt());

            builder.candidate(candidateDto);
            System.out.println("Successfully added candidate to invoice DTO: " + candidateDto.getName());
        } else {
            System.out.println("No candidate found in invoice entity");
        }

        // Set staffing type if available
        if (invoice.getStaffingType() != null) {
            builder.staffingTypeId(invoice.getStaffingType().getId());

            // Create and set the staffing type DTO
            StaffingTypeDto staffingTypeDto = StaffingTypeDto.builder()
                    .id(invoice.getStaffingType().getId())
                    .name(invoice.getStaffingType().getName())
                    .build();

            // Set audit fields for staffing type
            staffingTypeDto.setCreatedAt(invoice.getStaffingType().getCreatedAt());
            staffingTypeDto.setUpdatedAt(invoice.getStaffingType().getModifiedAt());

            builder.staffingType(staffingTypeDto);
        }

        // Set HSN code if available
        if (invoice.getHsnCode() != null) {
            builder.hsnId(invoice.getHsnCode().getId());

            // Create and set the HSN code DTO
            HsnCodeDto hsnCodeDto = HsnCodeDto.builder()
                    .id(invoice.getHsnCode().getId())
                    .code(invoice.getHsnCode().getCode())
                    .description(invoice.getHsnCode().getDescription())
                    .gstRate(invoice.getHsnCode().getGstRate())
                    .build();

            // Set audit fields for HSN code
            hsnCodeDto.setCreatedAt(invoice.getHsnCode().getCreatedAt());
            hsnCodeDto.setUpdatedAt(invoice.getHsnCode().getModifiedAt());

            builder.hsnCode(hsnCodeDto);
        }

        // Set Redberyl account if available
        if (invoice.getRedberylAccount() != null) {
            builder.redberylAccountId(invoice.getRedberylAccount().getId());

            // Create and set the Redberyl account DTO
            RedberylAccountDto redberylAccountDto = RedberylAccountDto.builder()
                    .id(invoice.getRedberylAccount().getId())
                    .accountName(invoice.getRedberylAccount().getAccountName())
                    .accountNo(invoice.getRedberylAccount().getAccountNo())
                    .bankName(invoice.getRedberylAccount().getBankName())
                    .branchName(invoice.getRedberylAccount().getBranchName())
                    .build();

            // Set audit fields for Redberyl account
            redberylAccountDto.setCreatedAt(invoice.getRedberylAccount().getCreatedAt());
            redberylAccountDto.setUpdatedAt(invoice.getRedberylAccount().getModifiedAt());

            builder.redberylAccount(redberylAccountDto);
        }

        // Build the DTO
        InvoiceDto dto = builder.build();

        // Set the audit fields
        dto.setCreatedAt(invoice.getCreatedAt());
        dto.setUpdatedAt(invoice.getModifiedAt());

        return dto;
    }

    private Invoice convertToEntity(InvoiceDto dto) {
        Invoice invoice = new Invoice();
        invoice.setId(dto.getId());
        invoice.setInvoiceNumber(dto.getInvoiceNumber());

        if (dto.getClientId() != null) {
            Client client = clientRepository.findById(dto.getClientId())
                    .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + dto.getClientId()));
            invoice.setClient(client);
        }

        if (dto.getInvoiceTypeId() != null) {
            InvoiceType invoiceType = invoiceTypeRepository.findById(dto.getInvoiceTypeId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Invoice Type not found with id: " + dto.getInvoiceTypeId()));
            invoice.setInvoiceType(invoiceType);
        }

        updateInvoiceFromDto(invoice, dto);

        return invoice;
    }

    private void updateInvoiceFromDto(Invoice invoice, InvoiceDto dto) {
        System.out.println("Updating invoice from DTO: " + dto);

        // Update invoice number if provided
        if (dto.getInvoiceNumber() != null && !dto.getInvoiceNumber().trim().isEmpty()) {
            invoice.setInvoiceNumber(dto.getInvoiceNumber());
            System.out.println("Updated invoice number: " + dto.getInvoiceNumber());
        }

        // Update client if provided
        if (dto.getClientId() != null) {
            Client client = clientRepository.findById(dto.getClientId())
                    .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + dto.getClientId()));
            invoice.setClient(client);
            System.out.println("Updated client: " + client.getName() + " (ID: " + client.getId() + ")");
        }

        // Update invoice type if provided
        if (dto.getInvoiceTypeId() != null) {
            InvoiceType invoiceType = invoiceTypeRepository.findById(dto.getInvoiceTypeId())
                    .orElseThrow(() -> new ResourceNotFoundException("Invoice Type not found with id: " + dto.getInvoiceTypeId()));
            invoice.setInvoiceType(invoiceType);
            System.out.println("Updated invoice type: " + invoiceType.getInvoiceType() + " (ID: " + invoiceType.getId() + ")");
        }

        // Update project if provided
        if (dto.getProjectId() != null) {
            Project project = projectRepository.findById(dto.getProjectId())
                    .orElseThrow(
                            () -> new ResourceNotFoundException("Project not found with id: " + dto.getProjectId()));
            invoice.setProject(project);
            System.out.println("Updated project: " + project.getName() + " (ID: " + project.getId() + ")");
        }

        // Update candidate if provided
        if (dto.getCandidateId() != null) {
            System.out.println("Setting candidate with ID: " + dto.getCandidateId());
            try {
                Candidate candidate = candidateRepository.findById(dto.getCandidateId())
                        .orElseThrow(
                                () -> new ResourceNotFoundException(
                                        "Candidate not found with id: " + dto.getCandidateId()));
                invoice.setCandidate(candidate);
                System.out.println("Successfully set candidate: " + candidate.getName() + " (ID: " + candidate.getId() + ")");
            } catch (Exception e) {
                System.err.println("Error setting candidate: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            System.out.println("No candidate ID provided in the DTO");
        }

        // Update staffing type if provided
        if (dto.getStaffingTypeId() != null) {
            StaffingType staffingType = staffingTypeRepository.findById(dto.getStaffingTypeId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Staffing Type not found with id: " + dto.getStaffingTypeId()));
            invoice.setStaffingType(staffingType);
            System.out.println("Updated staffing type: " + staffingType.getName() + " (ID: " + staffingType.getId() + ")");
        }

        // Update financial fields - ensure they're not null
        if (dto.getRate() != null) {
            invoice.setRate(dto.getRate());
            System.out.println("Updated rate: " + dto.getRate());
        }

        if (dto.getBillingAmount() != null) {
            invoice.setBillingAmount(dto.getBillingAmount());
            System.out.println("Updated billing amount: " + dto.getBillingAmount());
        }

        if (dto.getTaxAmount() != null) {
            invoice.setTaxAmount(dto.getTaxAmount());
            System.out.println("Updated tax amount: " + dto.getTaxAmount());
        }

        if (dto.getTotalAmount() != null) {
            invoice.setTotalAmount(dto.getTotalAmount());
            System.out.println("Updated total amount: " + dto.getTotalAmount());
        }

        // Update date fields
        if (dto.getInvoiceDate() != null) {
            invoice.setInvoiceDate(dto.getInvoiceDate());
            System.out.println("Updated invoice date: " + dto.getInvoiceDate());
        }

        if (dto.getDueDate() != null) {
            invoice.setDueDate(dto.getDueDate());
            System.out.println("Updated due date: " + dto.getDueDate());
        }

        // Update boolean fields - handle null values properly
        if (dto.getIsRecurring() != null) {
            invoice.setIsRecurring(dto.getIsRecurring());
        } else {
            invoice.setIsRecurring(false); // Default to false if not provided
        }

        if (dto.getPublishedToFinance() != null) {
            invoice.setPublishedToFinance(dto.getPublishedToFinance());
        } else {
            invoice.setPublishedToFinance(false); // Default to false if not provided
        }

        if (dto.getPublishedAt() != null) {
            invoice.setPublishedAt(dto.getPublishedAt());
        }

        // Update attendance days
        if (dto.getAttendanceDays() != null) {
            invoice.setAttendanceDays(dto.getAttendanceDays());
            System.out.println("Updated attendance days: " + dto.getAttendanceDays());
        }

        // Update HSN code if provided
        if (dto.getHsnId() != null) {
            HsnCode hsnCode = hsnCodeRepository.findById(dto.getHsnId())
                    .orElseThrow(() -> new ResourceNotFoundException("HSN Code not found with id: " + dto.getHsnId()));
            invoice.setHsnCode(hsnCode);
            System.out.println("Updated HSN code: " + hsnCode.getCode() + " (ID: " + hsnCode.getId() + ")");
        }

        // Update Redberyl account if provided
        if (dto.getRedberylAccountId() != null) {
            RedberylAccount redberylAccount = redberylAccountRepository.findById(dto.getRedberylAccountId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Redberyl Account not found with id: " + dto.getRedberylAccountId()));
            invoice.setRedberylAccount(redberylAccount);
            System.out.println("Updated Redberyl account: " + redberylAccount.getAccountName() + " (ID: " + redberylAccount.getId() + ")");
        }

        System.out.println("Invoice update completed successfully");
    }
}
