import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";

export interface LeadStatusOption {
  value: string;
  label: string;
  color: string;
}

interface LeadStatusDropdownProps {
  currentStatus: string;
  onStatusChange: (newStatus: string) => void;
}

const LeadStatusDropdown: React.FC<LeadStatusDropdownProps> = ({
  currentStatus,
  onStatusChange,
}) => {
  // Define lead status options
  const statusOptions: LeadStatusOption[] = [
    { value: "New", label: "New", color: "bg-blue-100 text-blue-800 border-blue-200" },
    { value: "Contacted", label: "Contacted", color: "bg-purple-100 text-purple-800 border-purple-200" },
    { value: "Qualified", label: "Qualified", color: "bg-green-100 text-green-800 border-green-200" },
    { value: "Not Interested", label: "Not Interested", color: "bg-red-100 text-red-800 border-red-200" },
  ];

  // Get the color for the current status
  const getCurrentStatusColor = () => {
    const status = statusOptions.find(s => s.value === currentStatus);
    return status ? status.color : "bg-gray-100 text-gray-800 border-gray-200";
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="cursor-pointer">
          <Badge
            className={`${getCurrentStatusColor()} px-2.5 py-0.5 rounded-full text-xs font-medium hover:opacity-80 transition-opacity`}
            variant="outline"
          >
            {currentStatus}
          </Badge>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[160px]">
        {statusOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onStatusChange(option.value);
            }}
            className="cursor-pointer"
          >
            <Badge className={`${option.color} w-full justify-center px-2.5 py-0.5 rounded-full text-xs font-medium`} variant="outline">
              {option.label}
            </Badge>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LeadStatusDropdown;
