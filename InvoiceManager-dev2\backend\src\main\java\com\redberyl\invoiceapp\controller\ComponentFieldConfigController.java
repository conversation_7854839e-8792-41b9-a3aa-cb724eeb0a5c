package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.MessageResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@CrossOrigin(origins = { "http://localhost:3060", "http://127.0.0.1:3060" }, allowedHeaders = "*", methods = {
        RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT,
        RequestMethod.DELETE, RequestMethod.OPTIONS }, allowCredentials = "true", maxAge = 3600)
@RestController

@Tag(name = "Component Field Config Management", description = "Component Field Config Management API")
@SecurityRequirement(name = "bearer-jwt")
public class ComponentFieldConfigController {

    @GetMapping("/component-field-configs/getById/{id}")
    @Operation(summary = "Get component field config by ID", description = "Get component field config by ID")
    public ResponseEntity<?> getById(
            @Parameter(description = "Field config ID", required = true) @PathVariable("id") Long id) {
        // This is a placeholder implementation
        Map<String, Object> response = new HashMap<>();
        response.put("id", id);
        response.put("name", "Sample Component Field");
        response.put("type", "text");
        response.put("required", true);
        response.put("componentId", 1);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/component-field-configs/getByFieldId/{componentId}/{id}")
    @Operation(summary = "Get component field configs by component field ID", description = "Get component field configs by component field ID")
    public ResponseEntity<?> getByFieldId(
            @Parameter(description = "Component ID", required = true) @PathVariable("componentId") Long componentId,
            @Parameter(description = "Field ID", required = true) @PathVariable("id") Long id) {
        // This is a placeholder implementation
        Map<String, Object> response = new HashMap<>();
        response.put("id", id);
        response.put("componentId", componentId);
        response.put("fields", new Object[] {
                Map.of("name", "Component Field 1", "type", "text", "required", true),
                Map.of("name", "Component Field 2", "type", "number", "required", false)
        });

        return ResponseEntity.ok(response);
    }

    @GetMapping("/component-field-configs/getAll")
    @Operation(summary = "Get all component field configs", description = "Get all component field configs")
    public ResponseEntity<?> getAll() {
        // This is a placeholder implementation
        return ResponseEntity.ok(new Object[] {
                Map.of("id", 1, "name", "Header Title", "type", "text", "required", true, "componentId", 1),
                Map.of("id", 2, "name", "Footer Text", "type", "textarea", "required", false, "componentId", 2),
                Map.of("id", 3, "name", "Button Label", "type", "text", "required", true, "componentId", 3)
        });
    }

    @PostMapping("/component-field-configs/create")
    @Operation(summary = "Create a new component field config", description = "Create a new component field config")
    public ResponseEntity<?> create(@RequestBody Map<String, Object> fieldConfig) {
        // This is a placeholder implementation
        Map<String, Object> response = new HashMap<>(fieldConfig);
        response.put("id", 999); // Simulated ID generation

        return ResponseEntity.ok(response);
    }

    @PutMapping("/component-field-configs/update/{id}")
    @Operation(summary = "Update a component field config", description = "Update a component field config")
    public ResponseEntity<?> update(
            @Parameter(description = "Field config ID", required = true) @PathVariable("id") Long id,
            @RequestBody Map<String, Object> fieldConfig) {
        // This is a placeholder implementation
        Map<String, Object> response = new HashMap<>(fieldConfig);
        response.put("id", id);

        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/component-field-configs/deleteById/{id}")
    @Operation(summary = "Delete a component field config", description = "Delete a component field config")
    public ResponseEntity<?> deleteById(
            @Parameter(description = "Field config ID", required = true) @PathVariable("id") Long id) {
        // This is a placeholder implementation
        return ResponseEntity
                .ok(new MessageResponseDto("Component field config with ID " + id + " deleted successfully"));
    }
}
