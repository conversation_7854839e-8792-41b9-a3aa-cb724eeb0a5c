import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, Info, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import oneDriveService from '@/services/oneDriveService';

interface OneDriveDebugProps {
  className?: string;
}

const OneDriveDebug: React.FC<OneDriveDebugProps> = ({ className = '' }) => {
  const [authStatus, setAuthStatus] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [authUrl, setAuthUrl] = useState<string>('');
  const [debugInfo, setDebugInfo] = useState<any>({});

  const checkStatus = async () => {
    setIsChecking(true);
    try {
      // Check authentication status
      const status = await oneDriveService.checkAuthentication();
      setAuthStatus(status);

      // Get auth URL for debugging
      const urlResponse = await oneDriveService.getAuthorizationUrl();
      if (urlResponse.success && urlResponse.authUrl) {
        setAuthUrl(urlResponse.authUrl);
      }

      // Collect debug info
      const debug = {
        hasStoredToken: !!localStorage.getItem('onedrive_access_token'),
        currentToken: localStorage.getItem('onedrive_access_token')?.substring(0, 20) + '...',
        isAuthenticated: oneDriveService.isAuthenticated(),
        currentUrl: window.location.href,
        origin: window.location.origin,
        timestamp: new Date().toISOString()
      };
      setDebugInfo(debug);

    } catch (error) {
      console.error('Error checking OneDrive status:', error);
      toast.error('Failed to check OneDrive status');
    } finally {
      setIsChecking(false);
    }
  };

  const testPopupAuth = async () => {
    try {
      toast.info('Testing popup authentication...');
      const result = await oneDriveService.authenticate();
      
      if (result.success) {
        toast.success('Popup authentication successful!');
        checkStatus();
      } else {
        toast.error(`Popup authentication failed: ${result.error}`);
      }
    } catch (error) {
      toast.error('Popup authentication error: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  const testDeviceAuth = async () => {
    try {
      toast.info('Testing device code authentication...');
      const result = await oneDriveService.authenticateWithDeviceCode();
      
      if (result.success) {
        toast.success('Device code authentication successful!');
        checkStatus();
      } else {
        toast.error(`Device code authentication failed: ${result.error}`);
      }
    } catch (error) {
      toast.error('Device code authentication error: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  const clearAuth = () => {
    oneDriveService.clearAuthentication();
    toast.info('Authentication cleared');
    checkStatus();
  };

  const copyAuthUrl = () => {
    if (authUrl) {
      navigator.clipboard.writeText(authUrl);
      toast.success('Auth URL copied to clipboard');
    }
  };

  useEffect(() => {
    checkStatus();
  }, []);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Info className="h-5 w-5" />
          OneDrive Debug Panel
        </CardTitle>
        <CardDescription>
          Debug OneDrive authentication and connection issues
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Section */}
        <div className="space-y-2">
          <h4 className="font-semibold">Authentication Status</h4>
          <div className="flex items-center gap-2">
            {authStatus?.authenticated ? (
              <Badge variant="default" className="bg-green-500">
                <CheckCircle className="h-3 w-3 mr-1" />
                Authenticated
              </Badge>
            ) : (
              <Badge variant="destructive">
                <AlertCircle className="h-3 w-3 mr-1" />
                Not Authenticated
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={checkStatus}
              disabled={isChecking}
            >
              {isChecking ? (
                <RefreshCw className="h-3 w-3 animate-spin" />
              ) : (
                <RefreshCw className="h-3 w-3" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        {/* Debug Info */}
        <div className="space-y-2">
          <h4 className="font-semibold">Debug Information</h4>
          <div className="bg-gray-50 p-3 rounded text-sm font-mono">
            <div>Has Stored Token: {debugInfo.hasStoredToken ? 'Yes' : 'No'}</div>
            <div>Service Auth Check: {debugInfo.isAuthenticated ? 'Yes' : 'No'}</div>
            <div>Current Origin: {debugInfo.origin}</div>
            <div>Timestamp: {debugInfo.timestamp}</div>
            {authStatus?.error && (
              <div className="text-red-600">Error: {authStatus.error}</div>
            )}
          </div>
        </div>

        {/* Auth URL */}
        {authUrl && (
          <div className="space-y-2">
            <h4 className="font-semibold">Authorization URL</h4>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={copyAuthUrl}>
                Copy Auth URL
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => window.open(authUrl, '_blank')}
              >
                Open in New Tab
              </Button>
            </div>
          </div>
        )}

        {/* Test Actions */}
        <div className="space-y-2">
          <h4 className="font-semibold">Test Authentication</h4>
          <div className="flex gap-2 flex-wrap">
            <Button variant="outline" size="sm" onClick={testPopupAuth}>
              Test Popup Auth
            </Button>
            <Button variant="outline" size="sm" onClick={testDeviceAuth}>
              Test Device Code Auth
            </Button>
            <Button variant="destructive" size="sm" onClick={clearAuth}>
              Clear Auth
            </Button>
          </div>
        </div>

        {/* Instructions */}
        <div className="space-y-2">
          <h4 className="font-semibold">Troubleshooting</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <p>1. Ensure redirect URI is registered in Azure Portal:</p>
            <p className="font-mono bg-gray-100 p-1 rounded">http://localhost:8091/api/onedrive/callback</p>
            <p>2. Check browser console for detailed error messages</p>
            <p>3. Try device code authentication if popup fails</p>
            <p>4. Verify Azure app credentials in backend configuration</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OneDriveDebug;
