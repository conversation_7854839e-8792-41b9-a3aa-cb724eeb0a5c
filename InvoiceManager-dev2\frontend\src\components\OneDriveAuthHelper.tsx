import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, ExternalLink, Copy, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

interface AuthStep {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'active' | 'completed' | 'error';
}

const OneDriveAuthHelper: React.FC = () => {
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [authData, setAuthData] = useState<any>(null);
  const [steps, setSteps] = useState<AuthStep[]>([
    { id: 1, title: 'Generate Device Code', description: 'Request authentication code from Microsoft', status: 'pending' },
    { id: 2, title: 'Open Microsoft Page', description: 'Navigate to Microsoft verification page', status: 'pending' },
    { id: 3, title: 'Enter Code', description: 'Enter the device code on Microsoft page', status: 'pending' },
    { id: 4, title: 'Sign In', description: 'Complete Microsoft account sign-in', status: 'pending' },
    { id: 5, title: 'Grant Permissions', description: 'Allow app to access OneDrive', status: 'pending' },
    { id: 6, title: 'Complete', description: 'Authentication successful', status: 'pending' }
  ]);
  const [pollCount, setPollCount] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);

  const updateStepStatus = (stepId: number, status: AuthStep['status']) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, status } : step
    ));
  };

  const startAuthentication = async () => {
    setIsAuthenticating(true);
    setAuthData(null);
    setPollCount(0);
    setTimeElapsed(0);
    
    // Reset all steps
    setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));

    try {
      // Step 1: Generate Device Code
      updateStepStatus(1, 'active');
      
      const deviceResponse = await fetch('/api/onedrive/device-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!deviceResponse.ok) {
        throw new Error(`Failed to generate device code: ${deviceResponse.status}`);
      }

      const deviceData = await deviceResponse.json();
      
      if (!deviceData.success) {
        throw new Error(deviceData.error || 'Failed to generate device code');
      }

      updateStepStatus(1, 'completed');
      setAuthData(deviceData);

      // Step 2: Copy code and open page
      updateStepStatus(2, 'active');
      
      try {
        await navigator.clipboard.writeText(deviceData.user_code);
        toast.success('Code copied to clipboard!');
      } catch (e) {
        console.log('Could not copy to clipboard');
      }

      updateStepStatus(2, 'completed');
      updateStepStatus(3, 'active');

      // Start polling
      startPolling(deviceData.device_code, deviceData.interval || 5);

    } catch (error) {
      console.error('Authentication failed:', error);
      updateStepStatus(1, 'error');
      toast.error('Authentication failed', {
        description: error instanceof Error ? error.message : 'Unknown error'
      });
      setIsAuthenticating(false);
    }
  };

  const startPolling = async (deviceCode: string, interval: number) => {
    const maxPolls = Math.floor((15 * 60) / interval); // 15 minutes
    let currentPoll = 0;

    const poll = async () => {
      if (currentPoll >= maxPolls) {
        updateStepStatus(6, 'error');
        toast.error('Authentication timeout');
        setIsAuthenticating(false);
        return;
      }

      currentPoll++;
      setPollCount(currentPoll);
      setTimeElapsed(currentPoll * interval);

      try {
        const tokenResponse = await fetch('/api/onedrive/device-token', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ device_code: deviceCode })
        });

        if (!tokenResponse.ok) {
          console.error(`Poll failed: ${tokenResponse.status}`);
          setTimeout(poll, interval * 1000);
          return;
        }

        const tokenData = await tokenResponse.json();
        console.log(`Poll ${currentPoll}:`, tokenData);

        if (tokenData.success && tokenData.access_token) {
          // Success!
          updateStepStatus(3, 'completed');
          updateStepStatus(4, 'completed');
          updateStepStatus(5, 'completed');
          updateStepStatus(6, 'completed');
          
          localStorage.setItem('onedrive_access_token', tokenData.access_token);
          
          toast.success('Authentication Successful!', {
            description: 'You can now save invoices to OneDrive'
          });
          
          setIsAuthenticating(false);
          return;
        }

        if (tokenData.error === 'authorization_pending') {
          // Still waiting - update UI based on progress
          if (currentPoll > 5) updateStepStatus(4, 'active');
          if (currentPoll > 10) updateStepStatus(5, 'active');
          
          setTimeout(poll, interval * 1000);
          return;
        }

        if (tokenData.error === 'authorization_declined') {
          updateStepStatus(4, 'error');
          toast.error('Authentication declined by user');
          setIsAuthenticating(false);
          return;
        }

        if (tokenData.error === 'expired_token') {
          updateStepStatus(6, 'error');
          toast.error('Authentication code expired');
          setIsAuthenticating(false);
          return;
        }

        // Other errors
        console.error('Unexpected error:', tokenData);
        setTimeout(poll, interval * 1000);

      } catch (error) {
        console.error('Polling error:', error);
        setTimeout(poll, interval * 1000);
      }
    };

    // Start polling after a short delay
    setTimeout(poll, interval * 1000);
  };

  const openMicrosoftPage = () => {
    if (authData?.verification_uri) {
      window.open(authData.verification_uri, '_blank');
    }
  };

  const copyCode = async () => {
    if (authData?.user_code) {
      try {
        await navigator.clipboard.writeText(authData.user_code);
        toast.success('Code copied to clipboard!');
      } catch (e) {
        toast.error('Could not copy to clipboard');
      }
    }
  };

  const getStepIcon = (status: AuthStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'active':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ExternalLink className="h-5 w-5 text-blue-500" />
          OneDrive Authentication Helper
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-3">
          <Button
            onClick={startAuthentication}
            disabled={isAuthenticating}
            className="gap-2"
          >
            {isAuthenticating ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ExternalLink className="h-4 w-4" />
            )}
            {isAuthenticating ? 'Authenticating...' : 'Start Authentication'}
          </Button>
        </div>

        {authData && (
          <Alert>
            <AlertDescription>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <strong>Device Code:</strong>
                  <code className="bg-gray-100 px-2 py-1 rounded text-lg font-mono">
                    {authData.user_code}
                  </code>
                  <Button size="sm" variant="outline" onClick={copyCode} className="gap-1">
                    <Copy className="h-3 w-3" />
                    Copy
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Button size="sm" onClick={openMicrosoftPage} className="gap-1">
                    <ExternalLink className="h-3 w-3" />
                    Open Microsoft Page
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {isAuthenticating && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>Poll #{pollCount}</span>
              <span>Time: {formatTime(timeElapsed)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min((timeElapsed / (15 * 60)) * 100, 100)}%` }}
              />
            </div>
          </div>
        )}

        <div className="space-y-2">
          <h4 className="font-semibold">Authentication Steps:</h4>
          {steps.map((step) => (
            <div key={step.id} className="flex items-center gap-3 p-2 rounded border">
              {getStepIcon(step.status)}
              <div>
                <div className="font-medium">{step.title}</div>
                <div className="text-sm text-gray-600">{step.description}</div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default OneDriveAuthHelper;
