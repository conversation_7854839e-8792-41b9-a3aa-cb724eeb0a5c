const AdminMaster = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h1 style={{ fontSize: '24px', marginBottom: '10px' }}>Role Master</h1>
      <p style={{ marginBottom: '20px' }}>This is a simple test page</p>
      <table style={{ width: '100%', borderCollapse: 'collapse' }}>
        <thead>
          <tr style={{ backgroundColor: '#f3f4f6' }}>
            <th style={{ padding: '10px', border: '1px solid #e5e7eb', textAlign: 'left' }}>ID</th>
            <th style={{ padding: '10px', border: '1px solid #e5e7eb', textAlign: 'left' }}>Name</th>
            <th style={{ padding: '10px', border: '1px solid #e5e7eb', textAlign: 'left' }}>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style={{ padding: '10px', border: '1px solid #e5e7eb' }}>1</td>
            <td style={{ padding: '10px', border: '1px solid #e5e7eb' }}>EMPLOYEE</td>
            <td style={{ padding: '10px', border: '1px solid #e5e7eb' }}>Employee</td>
          </tr>
          <tr>
            <td style={{ padding: '10px', border: '1px solid #e5e7eb' }}>2</td>
            <td style={{ padding: '10px', border: '1px solid #e5e7eb' }}>USER</td>
            <td style={{ padding: '10px', border: '1px solid #e5e7eb' }}>Specific access</td>
          </tr>
          <tr>
            <td style={{ padding: '10px', border: '1px solid #e5e7eb' }}>3</td>
            <td style={{ padding: '10px', border: '1px solid #e5e7eb' }}>ADMINISTRATOR</td>
            <td style={{ padding: '10px', border: '1px solid #e5e7eb' }}>Regular Admin role with permissions</td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default AdminMaster;
