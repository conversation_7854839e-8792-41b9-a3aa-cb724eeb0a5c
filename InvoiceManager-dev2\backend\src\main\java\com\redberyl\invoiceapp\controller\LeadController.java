package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.LeadDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.LeadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/leads")
@Tag(name = "Lead", description = "Lead management API")
@io.swagger.v3.oas.annotations.tags.Tag(name = "Lead Management", description = "API endpoints for managing leads")
public class LeadController {

    @Autowired
    private LeadService leadService;

    @GetMapping
    @Operation(summary = "Get all leads", description = "Get all leads")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Leads found"),
            @ApiResponse(responseCode = "204", description = "No leads found", content = @Content)
    })
    // @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<LeadDto>> getAllLeads() {
        try {
            List<LeadDto> leads = leadService.getAllLeads();
            return new ResponseEntity<>(leads, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get lead by ID", description = "Get lead by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Lead found"),
            @ApiResponse(responseCode = "404", description = "Lead not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    // @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<LeadDto> getLeadById(@PathVariable Long id) {
        LeadDto lead = leadService.getLeadById(id);
        return new ResponseEntity<>(lead, HttpStatus.OK);
    }

    @GetMapping("/status/{status}")
    @Operation(summary = "Get leads by status", description = "Get leads by status")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Leads found"),
            @ApiResponse(responseCode = "204", description = "No leads found with this status"),
            @ApiResponse(responseCode = "400", description = "Invalid status supplied")
    })
    // @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<LeadDto>> getLeadsByStatus(@PathVariable String status) {
        try {
            List<LeadDto> leads = leadService.getLeadsByStatus(status);
            return new ResponseEntity<>(leads, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/source/{source}")
    @Operation(summary = "Get leads by source", description = "Get leads by source")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Leads found"),
            @ApiResponse(responseCode = "204", description = "No leads found with this source"),
            @ApiResponse(responseCode = "400", description = "Invalid source supplied")
    })
    // @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<LeadDto>> getLeadsBySource(@PathVariable String source) {
        try {
            List<LeadDto> leads = leadService.getLeadsBySource(source);
            return new ResponseEntity<>(leads, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping(value = "", produces = "application/json")
    @Operation(
        summary = "Create a new lead",
        description = "Creates a new lead with the provided information",
        operationId = "createLead"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Lead created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    // @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<LeadDto> createLead(@Valid @RequestBody LeadDto leadDto) {
        LeadDto createdLead = leadService.createLead(leadDto);
        return new ResponseEntity<>(createdLead, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update lead", description = "Update lead")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Lead updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Lead not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    // @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<LeadDto> updateLead(@PathVariable Long id, @Valid @RequestBody LeadDto leadDto) {
        LeadDto updatedLead = leadService.updateLead(id, leadDto);
        return new ResponseEntity<>(updatedLead, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete lead", description = "Delete lead")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Lead deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or lead is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Lead not found")
    })
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteLead(@PathVariable Long id) {
        leadService.deleteLead(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @GetMapping("/check-email")
    @Operation(
        summary = "Check if email exists",
        description = "Checks if a lead with the given email already exists in the database",
        operationId = "checkEmailExists"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email check completed"),
            @ApiResponse(responseCode = "400", description = "Invalid email format")
    })
    public ResponseEntity<Object> checkEmailExists(@RequestParam String email) {
        if (email == null || email.trim().isEmpty()) {
            return ResponseEntity.badRequest().body(java.util.Collections.singletonMap("error", "Email cannot be empty"));
        }

        boolean exists = leadService.findByEmail(email).isPresent();
        return ResponseEntity.ok(java.util.Collections.singletonMap("exists", exists));
    }
}
