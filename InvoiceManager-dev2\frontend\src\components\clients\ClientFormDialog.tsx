
import { useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

const clientFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
});

type ClientFormValues = z.infer<typeof clientFormSchema> & {
  id?: number | string; // Add id field for editing existing clients
};

// Define a separate interface for the API payload
interface ClientApiPayload {
  id?: number;
  name: string;
}

interface ClientFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave?: (data: ClientApiPayload) => void;
  client?: ClientFormValues;
}

const ClientFormDialog = ({
  open,
  onOpenChange,
  onSave,
  client,
}: ClientFormDialogProps) => {
  const form = useForm<ClientFormValues>({
    resolver: zodResolver(clientFormSchema),
    defaultValues: client || {
      name: "",
    },
  });

  // Update form values when client prop changes or dialog opens
  useEffect(() => {
    console.log("ClientFormDialog: client prop changed or dialog opened", { client, open });

    if (open && client) {
      console.log("ClientFormDialog: Resetting form with client data", client);
      form.reset({
        name: client.name || "",
        id: client.id
      });
    } else if (open && !client) {
      console.log("ClientFormDialog: Resetting form for new client");
      form.reset({
        name: "",
      });
    }
  }, [client, open, form]);

  const onSubmit = (data: ClientFormValues) => {
    console.log("Form submitted with data:", data);

    // Ensure the data is properly formatted for API submission
    // Create a properly typed object for the API
    const apiPayload: ClientApiPayload = {
      name: data.name
    };

    // If we're editing an existing client, include the ID
    if (client?.id) {
      apiPayload.id = parseInt(String(client.id));
    }

    console.log("Formatted data for submission:", apiPayload);
    console.log("JSON payload:", JSON.stringify(apiPayload, null, 2));

    if (onSave) {
      try {
        // Call the onSave function with the formatted data
        onSave(apiPayload);
        // Don't close the dialog or show success message here
        // The parent component will handle this after API call completes
      } catch (error) {
        console.error("Error in onSave callback:", error);
        toast.error("Error saving client");
      }
    } else {
      // If no onSave provided (unlikely), show success and close dialog
      toast.success(`Client ${client ? "updated" : "created"} successfully!`);
      onOpenChange(false);
    }
  };



  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent className="w-[95%] sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{client ? "Edit Client" : "Add New Client"}</DialogTitle>
          <DialogDescription>
            {client
              ? "Update the client details below."
              : "Fill in the client details below to add them to your system."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Acme Corporation" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter className="w-full">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  console.log("Cancel button clicked");
                  onOpenChange(false);
                }}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="w-full sm:w-auto"
              >
                {client ? "Update Client" : "Add Client"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default ClientFormDialog;
