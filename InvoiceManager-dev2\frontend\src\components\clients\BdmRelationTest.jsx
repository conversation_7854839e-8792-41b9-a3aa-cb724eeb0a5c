import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Divider,
  Code,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  CardFooter
} from '@chakra-ui/react';
import BdmDropdown from '../BdmDropdown';

const BdmRelationTest = () => {
  const toast = useToast();
  
  // State for BDM data
  const [selectedBdmId, setSelectedBdmId] = useState(null);
  const [selectedBdm, setSelectedBdm] = useState(null);
  const [bdms, setBdms] = useState([]);
  const [bdmLoading, setBdmLoading] = useState(true);
  
  // State for client data
  const [clientData, setClientData] = useState({
    name: 'ABC Technologies Pvt Ltd',
    email: '<EMAIL>',
    phone: '+91-9876543210',
    contactPerson: '<PERSON><PERSON>',
    bdmId: null,
    commissionPercentage: '5'
  });
  
  // State for API responses
  const [bdmApiResponse, setBdmApiResponse] = useState(null);
  const [clientApiResponse, setClientApiResponse] = useState(null);
  
  // Fetch all BDMs for display
  useEffect(() => {
    const fetchAllBdms = async () => {
      try {
        setBdmLoading(true);
        
        // Create basic auth header if needed
        const authHeader = 'Basic ' + btoa('admin:admin123');
        
        const response = await fetch('http://localhost:8091/bdms', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          credentials: 'include'
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch BDMs: ${response.status}`);
        }
        
        const responseData = await response.json();
        console.log('BDM API response:', responseData);
        setBdmApiResponse(responseData);
        
        // Extract BDMs from the nested structure
        let bdmsData = [];
        
        if (responseData && responseData.success && responseData.data && responseData.data.content) {
          bdmsData = responseData.data.content;
        } else if (Array.isArray(responseData)) {
          bdmsData = responseData;
        } else if (responseData && responseData.data && Array.isArray(responseData.data)) {
          bdmsData = responseData.data;
        } else if (responseData && responseData.content && Array.isArray(responseData.content)) {
          bdmsData = responseData.content;
        } else {
          console.error('Unexpected BDM data format:', responseData);
          throw new Error('Unexpected data format from BDM API');
        }
        
        // Sort BDMs by name
        bdmsData.sort((a, b) => a.name.localeCompare(b.name));
        
        setBdms(bdmsData);
      } catch (err) {
        console.error('Error fetching all BDMs:', err);
        toast({
          title: 'Error fetching BDMs',
          description: err.message,
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      } finally {
        setBdmLoading(false);
      }
    };
    
    fetchAllBdms();
  }, [toast]);
  
  // Handle BDM selection
  const handleBdmChange = (bdmId) => {
    console.log('BDM selected:', bdmId);
    setSelectedBdmId(bdmId);
    
    // Find the selected BDM object
    const bdm = bdms.find(b => b.id === bdmId);
    setSelectedBdm(bdm);
    
    // Update client data
    setClientData(prev => ({ ...prev, bdmId }));
  };
  
  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setClientData(prev => ({ ...prev, [name]: value }));
  };
  
  // Create client with BDM relationship
  const handleCreateClient = async () => {
    try {
      if (!clientData.bdmId) {
        toast({
          title: 'BDM selection required',
          description: 'Please select a BDM before creating the client',
          status: 'warning',
          duration: 3060,
          isClosable: true
        });
        return;
      }
      
      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');
      
      // Make the API call to create the client
      const response = await fetch('{import.meta.env.VITE_API_URL}/clients', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        body: JSON.stringify(clientData),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to create client: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Client created successfully:', data);
      setClientApiResponse(data);
      
      toast({
        title: 'Client created successfully',
        description: `Client "${data.name}" has been created with BDM "${data.bdmName}"`,
        status: 'success',
        duration: 5000,
        isClosable: true
      });
    } catch (err) {
      console.error('Error creating client:', err);
      
      toast({
        title: 'Error creating client',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    }
  };
  
  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="lg">BDM Relationship Test</Heading>
          <Text mt={2} color="gray.600">
            This page demonstrates how the BDM dropdown works and how to create a client with a BDM relationship.
          </Text>
        </Box>
        
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
          {/* BDM Selection */}
          <Card>
            <CardHeader>
              <Heading size="md">BDM Selection</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <BdmDropdown 
                  value={selectedBdmId} 
                  onChange={handleBdmChange}
                  required={true}
                  label="Select a BDM"
                />
                
                {selectedBdm && (
                  <Box p={4} bg="blue.50" borderRadius="md">
                    <Heading size="sm" mb={2}>Selected BDM Details:</Heading>
                    <VStack align="stretch" spacing={1}>
                      <HStack>
                        <Text fontWeight="bold" width="120px">ID:</Text>
                        <Text>{selectedBdm.id}</Text>
                      </HStack>
                      <HStack>
                        <Text fontWeight="bold" width="120px">Name:</Text>
                        <Text>{selectedBdm.name}</Text>
                      </HStack>
                      <HStack>
                        <Text fontWeight="bold" width="120px">Email:</Text>
                        <Text>{selectedBdm.email || 'N/A'}</Text>
                      </HStack>
                      <HStack>
                        <Text fontWeight="bold" width="120px">Phone:</Text>
                        <Text>{selectedBdm.phone || 'N/A'}</Text>
                      </HStack>
                    </VStack>
                  </Box>
                )}
              </VStack>
            </CardBody>
          </Card>
          
          {/* Client Form */}
          <Card>
            <CardHeader>
              <Heading size="md">Create Client with BDM</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <FormControl isRequired>
                  <FormLabel>Company Name</FormLabel>
                  <Input 
                    name="name" 
                    value={clientData.name} 
                    onChange={handleInputChange} 
                  />
                </FormControl>
                
                <FormControl isRequired>
                  <FormLabel>Email</FormLabel>
                  <Input 
                    name="email" 
                    value={clientData.email} 
                    onChange={handleInputChange} 
                  />
                </FormControl>
                
                <FormControl isRequired>
                  <FormLabel>Phone</FormLabel>
                  <Input 
                    name="phone" 
                    value={clientData.phone} 
                    onChange={handleInputChange} 
                  />
                </FormControl>
                
                <FormControl isRequired>
                  <FormLabel>Contact Person</FormLabel>
                  <Input 
                    name="contactPerson" 
                    value={clientData.contactPerson} 
                    onChange={handleInputChange} 
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel>Commission Percentage</FormLabel>
                  <Input 
                    name="commissionPercentage" 
                    value={clientData.commissionPercentage} 
                    onChange={handleInputChange} 
                  />
                </FormControl>
              </VStack>
            </CardBody>
            <CardFooter>
              <Button 
                colorScheme="blue" 
                onClick={handleCreateClient}
                isDisabled={!selectedBdmId}
              >
                Create Client with BDM Relationship
              </Button>
            </CardFooter>
          </Card>
        </SimpleGrid>
        
        {/* All BDMs Table */}
        <Card>
          <CardHeader>
            <Heading size="md">All Available BDMs</Heading>
          </CardHeader>
          <CardBody>
            {bdmLoading ? (
              <Box textAlign="center" py={4}>
                <Spinner size="lg" />
                <Text mt={2}>Loading BDMs...</Text>
              </Box>
            ) : bdms.length === 0 ? (
              <Alert status="info">
                <AlertIcon />
                <AlertTitle>No BDMs found</AlertTitle>
                <AlertDescription>
                  There are no BDMs available in the system.
                </AlertDescription>
              </Alert>
            ) : (
              <Box overflowX="auto">
                <Table variant="simple" size="sm">
                  <Thead>
                    <Tr>
                      <Th>ID</Th>
                      <Th>Name</Th>
                      <Th>Email</Th>
                      <Th>Phone</Th>
                      <Th>Commission Rate</Th>
                      <Th>Clients</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {bdms.map(bdm => (
                      <Tr key={bdm.id}>
                        <Td>{bdm.id}</Td>
                        <Td fontWeight="medium">{bdm.name}</Td>
                        <Td>{bdm.email || '-'}</Td>
                        <Td>{bdm.phone || '-'}</Td>
                        <Td>{bdm.commissionRate || '0'}%</Td>
                        <Td>{bdm.clientCount || 0}</Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            )}
          </CardBody>
        </Card>
        
        {/* API Response */}
        {clientApiResponse && (
          <Card>
            <CardHeader>
              <Heading size="md">Client Creation Response</Heading>
            </CardHeader>
            <CardBody>
              <Box p={4} bg="green.50" borderRadius="md">
                <Heading size="sm" mb={2}>Created Client with BDM Relationship:</Heading>
                <VStack align="stretch" spacing={2}>
                  <HStack>
                    <Text fontWeight="bold" width="150px">Client ID:</Text>
                    <Text>{clientApiResponse.id}</Text>
                  </HStack>
                  <HStack>
                    <Text fontWeight="bold" width="150px">Client Name:</Text>
                    <Text>{clientApiResponse.name}</Text>
                  </HStack>
                  <HStack>
                    <Text fontWeight="bold" width="150px">BDM ID:</Text>
                    <Text>{clientApiResponse.bdmId}</Text>
                  </HStack>
                  <HStack>
                    <Text fontWeight="bold" width="150px">BDM Name:</Text>
                    <Text>{clientApiResponse.bdmName}</Text>
                  </HStack>
                </VStack>
                
                <Divider my={4} />
                
                <Heading size="sm" mb={2}>Full Response:</Heading>
                <Box overflowX="auto" maxH="300px" overflowY="auto">
                  <Code p={2} display="block" whiteSpace="pre">
                    {JSON.stringify(clientApiResponse, null, 2)}
                  </Code>
                </Box>
              </Box>
            </CardBody>
          </Card>
        )}
      </VStack>
    </Container>
  );
};

export default BdmRelationTest;
