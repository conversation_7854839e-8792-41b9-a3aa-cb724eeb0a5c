package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.BdmPayment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BdmPaymentRepository extends JpaRepository<BdmPayment, Long> {
    List<BdmPayment> findByBdmId(Long bdmId);
    List<BdmPayment> findByInvoiceId(Long invoiceId);
}
