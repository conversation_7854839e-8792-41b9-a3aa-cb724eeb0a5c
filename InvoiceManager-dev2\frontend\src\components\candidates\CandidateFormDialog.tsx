import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import candidateService from "@/services/candidateService";
import { clientService, Client } from "@/services/clientService";
import { projectService, Project } from "@/services/projectService";
// We don't need useNavigate anymore
// import { useNavigate } from "react-router-dom";

interface CandidateFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  candidateId?: string;
  defaultValues?: {
    name: string;
    email: string;
    phone: string;
    position: string;
    status: string;
    skills: string;
    experience: string;
    notes: string;
    clientId?: string;
    projectId?: string;
    joiningDate?: string;
    billingRate?: string;
    panNo?: string;
    aadharNo?: string;
    uanNo?: string;
    bankAccountNo?: string;
    branchName?: string;
    ifscCode?: string;
    address?: string;
    salaryOffered?: string;
  };
  onSuccess?: () => void;
}

const CandidateFormDialog: React.FC<CandidateFormDialogProps> = ({
  open,
  onOpenChange,
  candidateId,
  defaultValues,
  onSuccess,
}) => {
  const isEditing = !!candidateId;

  // State for clients
  const [clients, setClients] = useState<Client[]>([]);
  const [clientsLoading, setClientsLoading] = useState(true);
  const [clientsError, setClientsError] = useState<Error | null>(null);

  // State for projects
  const [projects, setProjects] = useState<Project[]>([]);
  const [projectsLoading, setProjectsLoading] = useState(true);
  const [projectsError, setProjectsError] = useState<Error | null>(null);

  // State to track filtered projects based on selected client
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    position: "",
    status: "New",
    skills: "",
    experience: "",
    notes: "",
    clientId: "",
    projectId: "",
    joiningDate: "",
    billingRate: "",
    panNo: "",
    aadharNo: "",
    uanNo: "",
    bankAccountNo: "",
    branchName: "",
    ifscCode: "",
    address: "",
    salaryOffered: ""
  });

  // We don't need navigate anymore since we're using the onSuccess callback
  // const navigate = useNavigate();

  // Fetch clients when component mounts
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setClientsLoading(true);
        setClientsError(null);
        console.log('Fetching clients for candidate form...');

        // Test direct API call first
        console.log('Testing direct API call to http://localhost:8091/clients');
        const testResponse = await fetch('http://localhost:8091/clients');
        console.log('Direct API test response status:', testResponse.status);
        if (testResponse.ok) {
          const testData = await testResponse.json();
          console.log('Direct API test data:', testData);
        }

        const data = await clientService.getAllClients();
        console.log('Clients fetched successfully:', data);
        console.log('Number of clients loaded:', data.length);

        if (Array.isArray(data) && data.length > 0) {
          setClients(data);
        } else {
          console.warn('No clients returned from service');
          setClients([]);
          toast.error('No clients found. Please add clients to the database.');
        }
      } catch (error) {
        console.error('Error fetching clients:', error);
        setClientsError(error as Error);
        setClients([]);
        toast.error('Failed to load clients. Please check your connection.');
      } finally {
        setClientsLoading(false);
      }
    };

    fetchClients();
  }, []);

  // Fetch projects when component mounts
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setProjectsLoading(true);
        setProjectsError(null);
        console.log('Fetching projects for candidate form...');

        // Test direct API call first
        console.log('Testing direct API call to http://localhost:8091/projects');
        const testResponse = await fetch('http://localhost:8091/projects');
        console.log('Direct API test response status:', testResponse.status);
        if (testResponse.ok) {
          const testData = await testResponse.json();
          console.log('Direct API test data:', testData);
        }

        const data = await projectService.getAllProjects();
        console.log('Projects fetched successfully:', data);
        console.log('Number of projects loaded:', data.length);

        if (Array.isArray(data) && data.length > 0) {
          setProjects(data);
        } else {
          console.warn('No projects returned from API');
          setProjects([]);
          toast.error('No projects found. Please add projects to the database.');
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
        setProjectsError(error as Error);
        setProjects([]);
        toast.error('Failed to load projects. Please check your connection.');
      } finally {
        setProjectsLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // Filter projects when client selection changes
  useEffect(() => {
    if (formData.clientId && projects.length > 0) {
      console.log(`Filtering projects for client ID: ${formData.clientId}`);
      const clientProjects = projects.filter(
        project => project.clientId === parseInt(formData.clientId, 10)
      );
      console.log('Filtered projects:', clientProjects);
      setFilteredProjects(clientProjects);
    } else {
      // If no client is selected, show all projects
      setFilteredProjects(projects);
    }
  }, [formData.clientId, projects]);

  // Debug effect to log when clients and projects change
  useEffect(() => {
    console.log("Clients updated:", clients.length, clients);
    console.log("Projects updated:", projects.length, projects);
    console.log("Current form clientId:", formData.clientId);
    console.log("Current form projectId:", formData.projectId);

    // Check if the current clientId exists in the clients array
    if (formData.clientId && clients.length > 0) {
      const matchingClient = clients.find(client => String(client.id) === String(formData.clientId));
      console.log("Matching client found:", matchingClient);
      if (!matchingClient) {
        console.warn("No matching client found for clientId:", formData.clientId);
        console.log("Available client IDs:", clients.map(c => String(c.id)));
      }
    }

    // Check if the current projectId exists in the projects array
    if (formData.projectId && projects.length > 0) {
      const matchingProject = projects.find(project => String(project.id) === String(formData.projectId));
      console.log("Matching project found:", matchingProject);
      if (!matchingProject) {
        console.warn("No matching project found for projectId:", formData.projectId);
        console.log("Available project IDs:", projects.map(p => String(p.id)));
      }
    }
  }, [clients, projects, formData.clientId, formData.projectId]);

  // Re-populate form data when clients/projects are loaded and we have defaultValues
  useEffect(() => {
    if (open && defaultValues && clients.length > 0 && projects.length > 0) {
      console.log("Re-populating form data now that clients and projects are loaded");
      setFormData(prev => ({
        ...prev,
        clientId: defaultValues.clientId ? String(defaultValues.clientId) : "",
        projectId: defaultValues.projectId ? String(defaultValues.projectId) : "",
      }));
    }
  }, [open, defaultValues, clients.length, projects.length]);

  // Reset form data when dialog opens/closes or when defaultValues change
  useEffect(() => {
    if (open) {
      if (defaultValues) {
        // When editing, populate with existing data
        console.log("Populating candidate form with data:", defaultValues);
        console.log("Client ID from defaultValues:", defaultValues.clientId);
        console.log("Project ID from defaultValues:", defaultValues.projectId);
        console.log("Available clients:", clients);
        console.log("Available projects:", projects);
        setFormData({
          name: defaultValues.name || "",
          email: defaultValues.email || "",
          phone: defaultValues.phone || "",
          position: defaultValues.position || "",
          status: defaultValues.status || "New",
          clientId: defaultValues.clientId ? String(defaultValues.clientId) : "",
          projectId: defaultValues.projectId ? String(defaultValues.projectId) : "",
          staffingTypeId: defaultValues.staffingTypeId || "",
          rate: defaultValues.rate || "",
          billingType: defaultValues.billingType || "monthly",
          skills: defaultValues.skills || "",
          experience: defaultValues.experience || "",
          notes: defaultValues.notes || "",
          joiningDate: defaultValues.joiningDate || "",
          billingRate: defaultValues.billingRate || "",
          panNo: defaultValues.panNo || "",
          aadharNo: defaultValues.aadharNo || "",
          uanNo: defaultValues.uanNo || "",
          bankAccountNo: defaultValues.bankAccountNo || "",
          branchName: defaultValues.branchName || "",
          ifscCode: defaultValues.ifscCode || "",
          address: defaultValues.address || "",
          salaryOffered: defaultValues.salaryOffered || "",
          ...defaultValues
        });
      } else {
        // When creating new, reset to empty form
        setFormData({
          name: "",
          email: "",
          phone: "",
          position: "",
          status: "New",
          clientId: "",
          projectId: "",
          skills: "",
          experience: "",
          notes: "",
          joiningDate: "",
          billingRate: "",
          panNo: "",
          aadharNo: "",
          uanNo: "",
          bankAccountNo: "",
          branchName: "",
          ifscCode: "",
          address: "",
          salaryOffered: ""
        });
      }
    }
  }, [open, defaultValues]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    console.log('Form validation - formData:', formData);
    console.log('Name:', formData.name, 'Email:', formData.email, 'Position:', formData.position);

    if (!formData.name || !formData.email || !formData.position) {
      console.error('Validation failed - missing required fields:', {
        name: !formData.name ? 'missing' : 'present',
        email: !formData.email ? 'missing' : 'present',
        position: !formData.position ? 'missing' : 'present'
      });
      toast.error("Please fill in all required fields");
      return;
    }

    console.log('Form validation passed');

    // Show loading toast
    const loadingToast = toast.loading(isEditing ? "Updating candidate..." : "Adding candidate...");

    try {
      // Prepare data for API submission
      // Find the selected client and project objects
      const selectedClientId = parseInt(formData.clientId) || null;
      const selectedProjectId = parseInt(formData.projectId) || null;

      // Find the selected client from the clients array
      const selectedClient = selectedClientId
        ? clients.find(client => client.id === selectedClientId) || { id: selectedClientId, name: "Unknown Client" }
        : null;

      // Find the selected project from the projects array
      const selectedProject = selectedProjectId
        ? projects.find(project => project.id === selectedProjectId) || { id: selectedProjectId, name: "Unknown Project" }
        : null;

        // Create a clean project object without any extra properties
        const projectWithoutClient = selectedProject ? {
          ...selectedProject,
          // Explicitly cast to any to avoid TypeScript errors with property deletion
        } as any : null;

        if (projectWithoutClient) {
          // Remove any properties that might exist but aren't needed
          if ('client' in projectWithoutClient) delete projectWithoutClient.client;
          if ('bdm' in projectWithoutClient) delete projectWithoutClient.bdm;
          if ('hsnCode' in projectWithoutClient) delete projectWithoutClient.hsnCode;
          if ('created_at' in projectWithoutClient) delete projectWithoutClient.created_at;
          if ('updated_at' in projectWithoutClient) delete projectWithoutClient.updated_at;
        }

      // Create a simple project object with just the required fields
      let cleanProject = null;
      if (projectWithoutClient) {
        cleanProject = {
          id: projectWithoutClient.id,
          name: projectWithoutClient.name
        };

        // Add clientId if it exists
        if ('clientId' in projectWithoutClient) {
          (cleanProject as any).clientId = projectWithoutClient.clientId;
        }
      }

      const candidateData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        designation: formData.position,
        status: formData.status,
        clientId: selectedClientId,
        projectId: selectedProjectId,
        // Add client and project objects with their data
        client: selectedClient ? {
          id: selectedClient.id,
          name: selectedClient.name
        } : null,
        project: cleanProject,
        // Format joining date properly - handle both new dates (YYYY-MM-DD) and existing ISO dates
        joiningDate: formData.joiningDate ?
          (formData.joiningDate.includes('T') ? formData.joiningDate : formData.joiningDate + "T09:06:55.078Z")
          : null,
        billingRate: formData.billingRate ? parseInt(formData.billingRate) : null,
        panNo: formData.panNo || null,
        aadharNo: formData.aadharNo || null,
        uanNo: formData.uanNo || null,
        experienceInYrs: formData.experience ? parseInt(formData.experience) : null,
        bankAccountNo: formData.bankAccountNo || null,
        branchName: formData.branchName || null,
        ifscCode: formData.ifscCode || null,
        address: formData.address || null,
        salaryOffered: formData.salaryOffered ? parseInt(formData.salaryOffered) : null,
        skills: formData.skills || null,
        notes: formData.notes || null
      };

      console.log('Submitting candidate data:', candidateData);

      if (isEditing && candidateId) {
        // API call to update candidate
        await candidateService.updateCandidate(candidateId, candidateData);
        toast.success(`Candidate "${formData.name}" updated successfully`);
      } else {
        // API call to create candidate
        await candidateService.createCandidate(candidateData);
        toast.success(`Candidate "${formData.name}" added successfully`);
      }

      // Call onSuccess callback if provided to refresh the candidates list
      if (onSuccess) {
        onSuccess();
      }

      // Close the dialog
      onOpenChange(false);

      // Dismiss loading toast
      toast.dismiss(loadingToast);
    } catch (error) {
      console.error('Error saving candidate:', error);

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show error toast
      toast.error(isEditing
        ? `Failed to update candidate. Please try again.`
        : `Failed to add candidate. Please try again.`
      );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="px-6 pt-6 pb-2">
          <DialogTitle className="text-xl">{isEditing ? "Edit Candidate" : "Add New Candidate"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6 py-4 max-h-[70vh] overflow-y-auto px-6" autoComplete="off">
          <div className="border-b pb-4 mb-4">
            <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter candidate name"
                  autoComplete="off"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="Enter email address"
                  autoComplete="off"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="Enter phone number"
                  autoComplete="off"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="position">Designation *</Label>
                <Input
                  id="position"
                  name="position"
                  value={formData.position}
                  onChange={handleChange}
                  placeholder="Enter designation"
                  autoComplete="off"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleSelectChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="New">New</SelectItem>
                    <SelectItem value="Active">Active</SelectItem>
                    <SelectItem value="Inactive">Inactive</SelectItem>
                    <SelectItem value="Onboarding">Onboarding</SelectItem>
                    <SelectItem value="Terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="experience">Experience (in years)</Label>
                <Input
                  id="experience"
                  name="experience"
                  type="number"
                  step="0.1"
                  value={formData.experience}
                  onChange={handleChange}
                  placeholder="Enter years of experience"
                  autoComplete="off"
                />
              </div>
            </div>
          </div>

          <div className="border-b pb-4 mb-4">
            <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Project Assignment</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="clientId">Client</Label>
                <Select
                  value={formData.clientId}
                  onValueChange={(value) => handleSelectChange("clientId", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select client" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                    {clientsLoading ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        <span>Loading clients...</span>
                      </div>
                    ) : clients.length > 0 ? (
                      clients.map((client) => (
                        <SelectItem key={client.id} value={String(client.id)}>
                          {client.name}
                        </SelectItem>
                      ))
                    ) : (
                      <div className="p-2 text-center text-gray-500">
                        {clientsError ? "Error loading clients" : "No clients found"}
                      </div>
                    )}

                    {clientsError && (
                      <div className="p-2 border-t">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={() => {
                            setClientsLoading(true);
                            clientService.getAllClients()
                              .then(data => {
                                setClients(data);
                                setClientsError(null);
                              })
                              .catch(error => setClientsError(error as Error))
                              .finally(() => setClientsLoading(false));
                          }}
                        >
                          Retry loading clients
                        </Button>
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="projectId">Project</Label>
                <Select
                  value={formData.projectId}
                  onValueChange={(value) => handleSelectChange("projectId", value)}
                  disabled={!formData.clientId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={formData.clientId ? "Select project" : "Select client first"} />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                    {!formData.clientId ? (
                      <div className="p-2 text-center text-gray-500">
                        Please select a client first
                      </div>
                    ) : projectsLoading ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        <span>Loading projects...</span>
                      </div>
                    ) : filteredProjects.length > 0 ? (
                      filteredProjects.map((project) => (
                        <SelectItem key={project.id} value={String(project.id)}>
                          {project.name || `Project ${project.id}`}
                        </SelectItem>
                      ))
                    ) : (
                      <div className="p-2 text-center text-gray-500">
                        {projectsError ? "Error loading projects" : "No projects found for this client"}
                      </div>
                    )}

                    {/* Debug info - remove in production */}
                    <div className="p-2 border-t text-xs text-gray-500">
                      <div>Client ID: {formData.clientId || 'None'}</div>
                      <div>Total Projects: {projects.length}</div>
                      <div>Filtered Projects: {filteredProjects.length}</div>
                    </div>

                    {projectsError && (
                      <div className="p-2 border-t">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={() => {
                            setProjectsLoading(true);
                            projectService.getAllProjects()
                              .then(data => {
                                setProjects(data);
                                setProjectsError(null);
                              })
                              .catch(error => setProjectsError(error as Error))
                              .finally(() => setProjectsLoading(false));
                          }}
                        >
                          Retry loading projects
                        </Button>
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="joiningDate">Joining Date</Label>
                <Input
                  id="joiningDate"
                  name="joiningDate"
                  type="date"
                  value={formData.joiningDate}
                  onChange={handleChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="billingRate">Billing Rate</Label>
                <Input
                  id="billingRate"
                  name="billingRate"
                  type="number"
                  step="0.01"
                  value={formData.billingRate}
                  onChange={handleChange}
                  placeholder="Enter billing rate"
                />
              </div>
            </div>
          </div>

          <div className="border-b pb-4 mb-4">
            <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Personal Information</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="panNo">PAN Number</Label>
                <Input
                  id="panNo"
                  name="panNo"
                  value={formData.panNo}
                  onChange={handleChange}
                  placeholder="Enter PAN number"
                  autoComplete="off"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="aadharNo">Aadhar Number</Label>
                <Input
                  id="aadharNo"
                  name="aadharNo"
                  value={formData.aadharNo}
                  onChange={handleChange}
                  placeholder="Enter Aadhar number"
                  autoComplete="off"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="uanNo">UAN Number</Label>
                <Input
                  id="uanNo"
                  name="uanNo"
                  value={formData.uanNo}
                  onChange={handleChange}
                  placeholder="Enter UAN number"
                />
              </div>

              <div className="space-y-2 col-span-2">
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  placeholder="Enter address"
                  className="min-h-[80px]"
                />
              </div>
            </div>
          </div>

          <div className="border-b pb-4 mb-4">
            <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Banking Details</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bankAccountNo">Bank Account Number</Label>
                <Input
                  id="bankAccountNo"
                  name="bankAccountNo"
                  value={formData.bankAccountNo}
                  onChange={handleChange}
                  placeholder="Enter bank account number"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="branchName">Branch Name</Label>
                <Input
                  id="branchName"
                  name="branchName"
                  value={formData.branchName}
                  onChange={handleChange}
                  placeholder="Enter branch name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="ifscCode">IFSC Code</Label>
                <Input
                  id="ifscCode"
                  name="ifscCode"
                  value={formData.ifscCode}
                  onChange={handleChange}
                  placeholder="Enter IFSC code"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="salaryOffered">Salary Offered</Label>
                <Input
                  id="salaryOffered"
                  name="salaryOffered"
                  type="number"
                  step="0.01"
                  value={formData.salaryOffered}
                  onChange={handleChange}
                  placeholder="Enter salary offered"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="skills">Skills</Label>
            <Textarea
              id="skills"
              name="skills"
              value={formData.skills}
              onChange={handleChange}
              placeholder="Enter candidate skills (e.g. JavaScript, React, Node.js)"
              className="min-h-[80px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              placeholder="Enter any additional notes"
              className="min-h-[80px]"
            />
          </div>

          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0 px-6 py-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="w-full sm:w-auto order-2 sm:order-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="w-full sm:w-auto order-1 sm:order-2"
            >
              {isEditing ? "Update Candidate" : "Add Candidate"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CandidateFormDialog;
