package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.TaxRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TaxRateRepository extends JpaRepository<TaxRate, Long> {
    List<TaxRate> findByTaxTypeId(Long taxTypeId);
    List<TaxRate> findByEffectiveFromLessThanEqualAndEffectiveToGreaterThanEqual(LocalDate date, LocalDate sameDate);
}
