package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.InvoiceMilestoneDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.InvoiceMilestone;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.InvoiceMilestoneRepository;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.service.InvoiceMilestoneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class InvoiceMilestoneServiceImpl implements InvoiceMilestoneService {

    @Autowired
    private InvoiceMilestoneRepository invoiceMilestoneRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Override
    public List<InvoiceMilestoneDto> getAllInvoiceMilestones() {
        List<InvoiceMilestone> invoiceMilestones = invoiceMilestoneRepository.findAll();
        if (invoiceMilestones.isEmpty()) {
            throw new NoContentException("No invoice milestones found");
        }
        return invoiceMilestones.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public InvoiceMilestoneDto getInvoiceMilestoneById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice milestone ID cannot be null");
        }

        InvoiceMilestone invoiceMilestone = invoiceMilestoneRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice Milestone not found with id: " + id));
        return convertToDto(invoiceMilestone);
    }

    @Override
    public List<InvoiceMilestoneDto> getInvoiceMilestonesByInvoiceId(Long invoiceId) {
        if (invoiceId == null) {
            throw new NullConstraintViolationException("invoiceId", "Invoice ID cannot be null");
        }

        // Check if invoice exists
        if (!invoiceRepository.existsById(invoiceId)) {
            throw new ResourceNotFoundException("Invoice not found with id: " + invoiceId);
        }

        List<InvoiceMilestone> invoiceMilestones = invoiceMilestoneRepository.findByInvoiceId(invoiceId);
        if (invoiceMilestones.isEmpty()) {
            throw new NoContentException("No invoice milestones found for invoice with id: " + invoiceId);
        }

        return invoiceMilestones.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceMilestoneDto> getInvoiceMilestonesByDateBefore(LocalDate date) {
        if (date == null) {
            throw new NullConstraintViolationException("date", "Date cannot be null");
        }

        List<InvoiceMilestone> invoiceMilestones = invoiceMilestoneRepository.findByMilestoneDateBefore(date);
        if (invoiceMilestones.isEmpty()) {
            throw new NoContentException("No invoice milestones found due before date: " + date);
        }

        return invoiceMilestones.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateInvoiceMilestoneDto(InvoiceMilestoneDto invoiceMilestoneDto) {
        if (invoiceMilestoneDto == null) {
            throw new NullConstraintViolationException("invoiceMilestoneDto", "Invoice milestone data cannot be null");
        }

        if (invoiceMilestoneDto.getInvoiceId() == null) {
            throw new NullConstraintViolationException("invoiceId", "Invoice ID cannot be null");
        }

        if (!invoiceRepository.existsById(invoiceMilestoneDto.getInvoiceId())) {
            throw new ForeignKeyViolationException("invoiceId",
                    "Invoice not found with id: " + invoiceMilestoneDto.getInvoiceId());
        }

        if (!StringUtils.hasText(invoiceMilestoneDto.getDescription())) {
            throw new NullConstraintViolationException("description", "Description cannot be empty");
        }

        if (invoiceMilestoneDto.getAmount() == null) {
            throw new NullConstraintViolationException("amount", "Amount cannot be null");
        }

        if (invoiceMilestoneDto.getMilestoneDate() == null) {
            throw new NullConstraintViolationException("milestoneDate", "Milestone date cannot be null");
        }
    }

    @Override
    @Transactional
    public InvoiceMilestoneDto createInvoiceMilestone(InvoiceMilestoneDto invoiceMilestoneDto) {
        validateInvoiceMilestoneDto(invoiceMilestoneDto);

        try {
            InvoiceMilestone invoiceMilestone = convertToEntity(invoiceMilestoneDto);
            InvoiceMilestone savedInvoiceMilestone = invoiceMilestoneRepository.save(invoiceMilestone);
            return convertToDto(savedInvoiceMilestone);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating invoice milestone: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating invoice milestone", e);
        }
    }

    @Override
    @Transactional
    public InvoiceMilestoneDto updateInvoiceMilestone(Long id, InvoiceMilestoneDto invoiceMilestoneDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice milestone ID cannot be null");
        }

        InvoiceMilestone existingInvoiceMilestone = invoiceMilestoneRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice Milestone not found with id: " + id));

        try {
            if (StringUtils.hasText(invoiceMilestoneDto.getDescription())) {
                existingInvoiceMilestone.setDescription(invoiceMilestoneDto.getDescription());
            }

            if (invoiceMilestoneDto.getAmount() != null) {
                existingInvoiceMilestone.setAmount(invoiceMilestoneDto.getAmount());
            }

            if (invoiceMilestoneDto.getMilestoneDate() != null) {
                existingInvoiceMilestone.setMilestoneDate(invoiceMilestoneDto.getMilestoneDate());
            }

            InvoiceMilestone updatedInvoiceMilestone = invoiceMilestoneRepository.save(existingInvoiceMilestone);
            return convertToDto(updatedInvoiceMilestone);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating invoice milestone: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating invoice milestone", e);
        }
    }

    @Override
    @Transactional
    public void deleteInvoiceMilestone(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice milestone ID cannot be null");
        }

        if (!invoiceMilestoneRepository.existsById(id)) {
            throw new ResourceNotFoundException("Invoice Milestone not found with id: " + id);
        }

        try {
            invoiceMilestoneRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete invoice milestone because it is referenced by other entities",
                        e);
            } else {
                throw new CustomException("Error deleting invoice milestone: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting invoice milestone", e);
        }
    }

    private InvoiceMilestoneDto convertToDto(InvoiceMilestone invoiceMilestone) {
        return InvoiceMilestoneDto.builder()
                .id(invoiceMilestone.getId())
                .invoiceId(invoiceMilestone.getInvoice().getId())
                .description(invoiceMilestone.getDescription())
                .amount(invoiceMilestone.getAmount())
                .milestoneDate(invoiceMilestone.getMilestoneDate())
                .build();
    }

    private InvoiceMilestone convertToEntity(InvoiceMilestoneDto invoiceMilestoneDto) {
        InvoiceMilestone invoiceMilestone = new InvoiceMilestone();
        invoiceMilestone.setId(invoiceMilestoneDto.getId());

        if (invoiceMilestoneDto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(invoiceMilestoneDto.getInvoiceId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Invoice not found with id: " + invoiceMilestoneDto.getInvoiceId()));
            invoiceMilestone.setInvoice(invoice);
        }

        invoiceMilestone.setDescription(invoiceMilestoneDto.getDescription());
        invoiceMilestone.setAmount(invoiceMilestoneDto.getAmount());
        invoiceMilestone.setMilestoneDate(invoiceMilestoneDto.getMilestoneDate());

        return invoiceMilestone;
    }
}
