import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Cloud, CloudUpload, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import oneDriveService, { OneDriveUploadResponse } from '@/services/oneDriveService';

interface OneDriveButtonProps {
  pdfBlob?: Blob;
  pdfBase64?: string;
  invoiceNumber?: string;
  invoice?: any; // For automatic PDF generation
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  onUploadStart?: () => void;
  onUploadSuccess?: (response: OneDriveUploadResponse) => void;
  onUploadError?: (error: string) => void;
  onAuthRequired?: () => void;
}

const OneDriveButton: React.FC<OneDriveButtonProps> = ({
  pdfBlob,
  pdfBase64,
  invoiceNumber,
  invoice,
  disabled = false,
  variant = 'outline',
  size = 'default',
  className = '',
  onUploadStart,
  onUploadSuccess,
  onUploadError,
  onAuthRequired
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    setIsCheckingAuth(true);
    try {
      // Use the new async authentication check that handles token refresh
      const isAuthenticated = await oneDriveService.isAuthenticatedAsync();
      setIsAuthenticated(isAuthenticated);
      console.log('🔐 OneDrive authentication status:', isAuthenticated);
    } catch (error) {
      console.error('Error checking OneDrive authentication:', error);
      setIsAuthenticated(false);
    } finally {
      setIsCheckingAuth(false);
    }
  };

  const handleAuthenticate = async () => {
    try {
      setIsCheckingAuth(true);
      const authResult = await oneDriveService.authenticate();
      
      if (authResult.success) {
        setIsAuthenticated(true);
        toast.success('Successfully authenticated with OneDrive!');
        if (onAuthRequired) {
          onAuthRequired();
        }
      } else {
        toast.error(authResult.error || 'Failed to authenticate with OneDrive');
        if (onUploadError) {
          onUploadError(authResult.error || 'Authentication failed');
        }
      }
    } catch (error) {
      console.error('Authentication error:', error);
      toast.error('Authentication failed');
      if (onUploadError) {
        onUploadError('Authentication failed');
      }
    } finally {
      setIsCheckingAuth(false);
    }
  };

  const handleUpload = async () => {
    console.log('🔵 OneDriveButton clicked!', {
      invoice: invoice ? {
        id: invoice.id,
        databaseId: invoice.databaseId,
        invoiceNumber: invoice.invoiceNumber,
        client: invoice.client,
        amount: invoice.amount
      } : null,
      hasPdfBlob: !!pdfBlob,
      hasPdfBase64: !!pdfBase64,
      invoiceNumber
    });

    // Check if we have any content to upload before proceeding
    if (!pdfBlob && !pdfBase64 && !invoice) {
      console.log('❌ No PDF content or invoice provided');
      if (onUploadError) {
        onUploadError('No PDF content or invoice provided');
      }
      return;
    }

    // Call onUploadStart first to allow parent components to handle pre-upload logic
    if (onUploadStart) {
      try {
        onUploadStart();
      } catch (error) {
        // If onUploadStart throws an error or returns false, stop the upload
        console.log('❌ Upload cancelled by onUploadStart');
        return;
      }
    }

    if (!isAuthenticated) {
      await handleAuthenticate();
      return;
    }

    // If no PDF content is provided but we have an invoice, generate it
    if (!pdfBlob && !pdfBase64 && invoice) {
      setIsUploading(true);
      setUploadStatus('idle');

      try {
        console.log('🔄 Generating PDF for invoice:', invoice);

        // Use the database ID for the PDF endpoint, not the display ID
        const invoiceId = invoice.databaseId || invoice.id;
        if (!invoiceId) {
          throw new Error('Invoice ID not found');
        }
        console.log('📄 Using invoice ID for PDF:', invoiceId);

        // Use the direct PDF endpoint with database ID
        const pdfUrl = `/api/invoice-generation/pdf/${invoiceId}`;
        console.log('📄 Using PDF URL:', pdfUrl);

        const response = await fetch(pdfUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
        }

        const pdfBlob = await response.blob();

        // Upload to OneDrive using the display ID for the filename
        const uploadResponse = await oneDriveService.uploadPdf(pdfBlob, invoice.id);

        setUploadStatus('success');
        toast.success('Invoice saved to OneDrive successfully!');

        if (onUploadSuccess) {
          onUploadSuccess(uploadResponse);
        }
      } catch (error) {
        console.error('❌ OneDrive upload failed:', error);
        setUploadStatus('error');
        toast.error('Failed to save to OneDrive', {
          description: error instanceof Error ? error.message : 'Unknown error occurred'
        });

        if (onUploadError) {
          onUploadError(error instanceof Error ? error.message : 'Unknown error');
        }
      } finally {
        setIsUploading(false);
      }
      return;
    }



    setIsUploading(true);
    setUploadStatus('idle');

    if (onUploadStart) {
      onUploadStart();
    }

    try {
      let response: OneDriveUploadResponse;

      if (pdfBase64 && invoiceNumber) {
        response = await oneDriveService.uploadInvoicePdf(pdfBase64, invoiceNumber);
      } else if (pdfBlob) {
        response = await oneDriveService.uploadPdf(pdfBlob, invoiceNumber);
      } else {
        throw new Error('Invalid upload parameters');
      }

      if (response.success) {
        setUploadStatus('success');
        toast.success('PDF successfully saved to OneDrive!', {
          description: response.fileName ? `File: ${response.fileName}` : undefined,
          action: response.webUrl ? {
            label: 'Open in OneDrive',
            onClick: () => window.open(response.webUrl, '_blank')
          } : undefined
        });
        
        if (onUploadSuccess) {
          onUploadSuccess(response);
        }
      } else {
        setUploadStatus('error');
        toast.error('Failed to save PDF to OneDrive', {
          description: response.error || response.message
        });
        
        if (onUploadError) {
          onUploadError(response.error || response.message || 'Upload failed');
        }
      }
    } catch (error) {
      setUploadStatus('error');
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      toast.error('Failed to save PDF to OneDrive', {
        description: errorMessage
      });
      
      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setIsUploading(false);
      // Reset status after 3 seconds
      setTimeout(() => setUploadStatus('idle'), 3000);
    }
  };

  const getButtonIcon = () => {
    if (isCheckingAuth || isUploading) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    
    if (uploadStatus === 'success') {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    
    if (uploadStatus === 'error') {
      return <AlertCircle className="h-4 w-4 text-red-600" />;
    }
    
    if (!isAuthenticated) {
      return <Cloud className="h-4 w-4" />;
    }
    
    return <CloudUpload className="h-4 w-4" />;
  };

  const getButtonText = () => {
    if (isCheckingAuth) {
      return 'Checking...';
    }

    if (isUploading) {
      return 'Saving to OneDrive...';
    }

    if (uploadStatus === 'success') {
      return 'Saved to OneDrive';
    }

    if (uploadStatus === 'error') {
      return 'Upload Failed';
    }

    return 'Save to OneDrive';
  };

  const getButtonVariant = () => {
    if (uploadStatus === 'success') {
      return 'default';
    }
    
    if (uploadStatus === 'error') {
      return 'destructive';
    }
    
    return variant;
  };

  return (
    <Button
      onClick={handleUpload}
      disabled={disabled || isUploading || isCheckingAuth}
      variant={getButtonVariant()}
      size={size}
      className={`${className} transition-all duration-200 cursor-pointer hover:cursor-pointer`}
    >
      {getButtonIcon()}
      <span className="ml-2">{getButtonText()}</span>
    </Button>
  );
};

export default OneDriveButton;
