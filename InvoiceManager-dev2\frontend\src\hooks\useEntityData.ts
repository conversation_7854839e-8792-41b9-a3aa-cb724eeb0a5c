import React, { useState, useEffect, useRef } from 'react';
import { api } from '@/services/api';
import { clientService } from '@/services/clientService';
import { projectService } from '@/services/projectService';
import { hsnCodeService } from '@/services/hsnCodeService';
import { getProxiedUrl } from '@/utils/apiUtils';

type EntityType =
  | 'roles'
  | 'clients'
  | 'projects'
  | 'candidates'
  | 'spocs'
  | 'bdms'
  | 'staffingTypes'
  | 'invoiceTypes'
  | 'hsnCodes'
  | 'taxTypes'
  | 'taxRates'
  | 'redberylAccounts'
  | 'leads'
  | 'deals'
  | 'communications'
  | 'documentTemplates'
  | 'documentTemplateVersions'
  | 'documentVariables';

interface UseEntityDataProps {
  entityType: EntityType;
  filters?: Record<string, any>;
  useMockData?: boolean;
  refetchInterval?: number; // Add refetch interval in milliseconds
  cacheTime?: number; // Time in milliseconds to cache data
  fallbackToMockData?: boolean; // Whether to fall back to mock data if <PERSON> fails
}

interface UseEntityDataResult {
  data: any[];
  loading: boolean;
  error: Error | null;
  refetch: (forceRefresh?: boolean) => Promise<void>;
}



const apiMethodMap: Record<EntityType, () => Promise<any[]>> = {
  roles: api.getRoles,
  clients: clientService.getAllClients, // Use clientService instead of api
  projects: projectService.getAllProjects, // Use projectService instead of api
  candidates: api.getCandidates,
  spocs: api.getSpocs,
  bdms: api.getBdms,
  staffingTypes: api.getStaffingTypes,
  invoiceTypes: api.getInvoiceTypes,
  hsnCodes: hsnCodeService.getAllHsnCodes, // Use hsnCodeService instead of api
  taxTypes: api.getTaxTypes,
  taxRates: api.getTaxRates,
  redberylAccounts: api.getRedberylAccounts,
  leads: api.getLeads,
  deals: api.getDeals,
  communications: api.getCommunications,
  documentTemplates: api.getDocumentTemplates,
  documentTemplateVersions: api.getDocumentTemplateVersions,
  documentVariables: api.getDocumentVariables,
};

// Global cache to store data across components
const dataCache: Record<string, { data: any[], timestamp: number }> = {};

// Track in-flight requests to prevent duplicate API calls
const pendingRequests: Record<string, Promise<any[]>> = {};

// Global request lock to prevent any duplicate API calls
const requestLocks: Record<string, boolean> = {};

// Last successful fetch time to enforce minimum time between fetches
const lastFetchTimes: Record<string, number> = {};

// Cache expiration time (10 minutes - increased for stability)
const CACHE_EXPIRATION_TIME = 10 * 60 * 1000;

// Debounce time (1000ms - increased to prevent rapid calls)
const DEBOUNCE_TIME = 1000;

// Minimum time between fetches for the same entity (30 seconds)
const MIN_FETCH_INTERVAL = 30 * 1000;

// Mock data for fallback
const mockDataMap: Record<EntityType, any[]> = {
  roles: [],
  clients: [],
  projects: [],
  candidates: [],
  spocs: [],
  bdms: [],
  staffingTypes: [],
  invoiceTypes: [],
  hsnCodes: [],
  taxTypes: [],
  taxRates: [],
  redberylAccounts: [],
  leads: [],
  deals: [],
  communications: [],
  documentTemplates: [],
  documentTemplateVersions: [],
  documentVariables: []
};

export function useEntityData({
  entityType,
  filters = {},
  useMockData = false, // Default to using real API data
  refetchInterval = 0, // Default to no automatic refetching
  cacheTime = CACHE_EXPIRATION_TIME, // Default cache time is 5 minutes
  fallbackToMockData = false // Default to not falling back to mock data
}: UseEntityDataProps): UseEntityDataResult {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Create a cache key based on entity type
  const cacheKey = `${entityType}`;

  // Create a debounced version of the fetch function
  const debouncedFetchRef = useRef<NodeJS.Timeout | null>(null);

  const fetchData = async (forceRefresh = false) => {
    // Clear any existing debounce timer
    if (debouncedFetchRef.current) {
      clearTimeout(debouncedFetchRef.current);
    }

    // If forceRefresh is true, clear the cache for this entity
    if (forceRefresh) {
      console.log(`Force refreshing ${entityType} data - clearing cache`);
      delete dataCache[cacheKey];

      // Also clear the localStorage cache if it exists
      try {
        localStorage.removeItem(`${entityType}_cache`);
      } catch (e) {
        console.warn(`Could not clear localStorage cache for ${entityType}:`, e);
      }
    }

    // Return a promise that resolves when the debounced fetch completes
    return new Promise<void>((resolve) => {
      debouncedFetchRef.current = setTimeout(async () => {
        try {
          await actualFetchData(forceRefresh);
        } finally {
          resolve();
        }
      }, forceRefresh ? 0 : DEBOUNCE_TIME); // Skip debounce if force refreshing
    });
  };

  const actualFetchData = async (_forceRefresh = false) => {
    const now = Date.now();

    // STRICT PREVENTION: Check if this entity is locked (another component is fetching it)
    if (requestLocks[cacheKey]) {
      console.log(`Request for ${entityType} is locked, waiting for existing request to complete`);
      // If locked, wait for the pending request to complete
      if (pendingRequests[cacheKey]) {
        try {
          const result = await pendingRequests[cacheKey];
          setData(result);
          setLoading(false);
          setError(null);
          return;
        } catch (err) {
          console.error(`Error from pending request for ${entityType}:`, err);
          setError(err instanceof Error ? err : new Error(String(err)));
          setLoading(false);
          return;
        }
      } else if (dataCache[cacheKey]) {
        // If no pending request but we have cache, use it regardless of age
        console.log(`Using cached data for ${entityType} (forced due to lock)`);
        setData(dataCache[cacheKey].data);
        setLoading(false);
        setError(null);
        return;
      } else {
        // This should rarely happen - lock exists but no pending request or cache
        console.warn(`Request locked for ${entityType} but no pending request or cache found`);
        // Wait a bit and then release the lock
        await new Promise(resolve => setTimeout(resolve, 500));
        requestLocks[cacheKey] = false;
      }
    }

    // STRICT PREVENTION: Check if we've fetched this entity recently
    const lastFetchTime = lastFetchTimes[cacheKey] || 0;
    if ((now - lastFetchTime) < MIN_FETCH_INTERVAL) {
      console.log(`Request for ${entityType} was made too recently, enforcing minimum interval`);

      // If we have cache, use it regardless of age
      if (dataCache[cacheKey]) {
        console.log(`Using cached data for ${entityType} (enforced minimum interval)`);
        setData(dataCache[cacheKey].data);
        setLoading(false);
        setError(null);
        return;
      }
    }

    // Check if we have valid cached data
    if (dataCache[cacheKey] &&
        (now - dataCache[cacheKey].timestamp) < cacheTime) {
      console.log(`Using cached data for ${entityType} (age: ${(now - dataCache[cacheKey].timestamp)/1000}s)`);
      setData(dataCache[cacheKey].data);
      setLoading(false);
      setError(null);
      return;
    }

    // Check if there's already a pending request for this entity type
    if (pendingRequests[cacheKey]) {
      console.log(`Request for ${entityType} already in progress, joining existing request`);
      try {
        const result = await pendingRequests[cacheKey];
        setData(result);
        setLoading(false);
        setError(null);
        return;
      } catch (err) {
        console.error(`Error from pending request for ${entityType}:`, err);
        setError(err instanceof Error ? err : new Error(String(err)));
        setLoading(false);
        return;
      }
    }

    // STRICT PREVENTION: Set the lock before proceeding with API call
    requestLocks[cacheKey] = true;

    // No valid cache or pending request, proceed with API call
    setLoading(true);
    setError(null);

    try {
      let result: any[];

      // Get the appropriate API method for the entity type
      const apiMethod = apiMethodMap[entityType];
      if (!apiMethod) {
        throw new Error(`No API method found for entity type: ${entityType}`);
      }

      try {
        // First try to fetch data from API
        console.log(`Fetching ${entityType} from API...`);
        const apiUrl = getProxiedUrl(`/${entityType}`);
        console.log(`API URL for ${entityType}:`, apiUrl);

        // Create a promise for this request and store it in pendingRequests
        pendingRequests[cacheKey] = (async () => {
          try {
            const apiResponse = await apiMethod();

            // Process the response and return the data
            // This code is duplicated from below to handle the response inside the promise
            let processedResult: any[] = [];

            if (apiResponse && typeof apiResponse === 'object') {
              // Check if it's in ApiResponseDto format
              if ('data' in apiResponse && 'success' in apiResponse) {
                const apiResponseDto = apiResponse as { success: boolean; data: any; message?: string };
                if (apiResponseDto.success && apiResponseDto.data) {
                  if (Array.isArray(apiResponseDto.data)) {
                    processedResult = apiResponseDto.data;
                  } else if (typeof apiResponseDto.data === 'object') {
                    // Check for nested content
                    if ('content' in apiResponseDto.data && Array.isArray(apiResponseDto.data.content)) {
                      processedResult = apiResponseDto.data.content;
                    } else {
                      // Try to convert to array if it's a single object
                      processedResult = [apiResponseDto.data];
                    }
                  } else {
                    // If data is not an object or array, create an empty array
                    processedResult = [];
                  }
                } else {
                  throw new Error(apiResponseDto.message || `Error fetching ${entityType}`);
                }
              } else if (Array.isArray(apiResponse)) {
                // Direct array response
                processedResult = apiResponse;
              } else {
                // Check for common array properties
                const objResponse = apiResponse as Record<string, any>;

                if ('content' in objResponse && Array.isArray(objResponse.content)) {
                  processedResult = objResponse.content;
                } else if ('items' in objResponse && Array.isArray(objResponse.items)) {
                  processedResult = objResponse.items;
                } else if ('data' in objResponse) {
                  if (Array.isArray(objResponse.data)) {
                    processedResult = objResponse.data;
                  } else if (objResponse.data && typeof objResponse.data === 'object' && 'content' in objResponse.data && Array.isArray(objResponse.data.content)) {
                    processedResult = objResponse.data.content;
                  } else if (objResponse.data) {
                    // If data is not an array but exists, try to make it an array
                    processedResult = [objResponse.data];
                  }
                } else {
                  // Look for any array property
                  let foundArray = false;
                  for (const key in objResponse) {
                    if (Array.isArray(objResponse[key])) {
                      processedResult = objResponse[key];
                      foundArray = true;
                      break;
                    }
                  }

                  // If no array found but it's an object with id, treat as single item
                  if (!foundArray && 'id' in objResponse) {
                    processedResult = [objResponse];
                  }
                }
              }
            } else if (Array.isArray(apiResponse)) {
              // Direct array response
              processedResult = apiResponse;
            }

            // Ensure we have a valid array
            if (!processedResult || !Array.isArray(processedResult)) {
              processedResult = [];
            }

            return processedResult;
          } finally {
            // Remove this request from pendingRequests when it completes
            delete pendingRequests[cacheKey];
          }
        })();

        // Wait for the pending request to complete
        // The response is already processed inside the promise
        result = await pendingRequests[cacheKey];
        console.log(`Processed data for ${entityType}:`, result);

        console.log(`Successfully fetched ${entityType} from API:`, result);
      } catch (apiError) {
        // If API call fails and we're allowed to use mock data or fallback is enabled, fall back to it
        if (useMockData || fallbackToMockData) {
          console.warn(`API call failed for ${entityType}, falling back to mock data:`, apiError);
          result = [...mockDataMap[entityType]];
          // Simulate network delay
          await new Promise(resolve => setTimeout(resolve, 500));
        } else {
          // If we're not allowed to use mock data, try a direct fetch as a last resort
          console.error(`API call failed for ${entityType} and mock data not allowed:`, apiError);

          // Try a direct fetch for specific entity types
          if (entityType === 'clients' || entityType === 'hsnCodes' || entityType === 'projects' || entityType === 'bdms') {
            try {
              const endpoints = {
                'clients': ['http://localhost:8091/api/clients', 'http://localhost:8091/clients'],
                'hsnCodes': ['http://localhost:8091/api/hsn-codes', 'http://localhost:8091/hsn-codes'],
                'projects': [
                  'http://localhost:8091/api/projects',
                  'http://localhost:8091/projects',
                  'http://localhost:8091/api/noauth/getProjects'
                ],
                'bdms': [
                  'http://localhost:8091/bdms',        // Direct to BdmCompatController - primary
                  'http://localhost:8091/v1/bdms',     // Direct to BdmController (backend) - paginated
                  '/bdms',                              // Proxied BdmCompatController
                  '/v1/bdms'                           // Proxied BdmController (backend)
                ]
              };

              const entityEndpoints = endpoints[entityType] || [];

              for (const endpoint of entityEndpoints) {
                try {
                  console.log(`Trying direct fetch for ${entityType} from ${endpoint}...`);

                  // Use getProxiedUrl for relative endpoints, direct fetch for absolute URLs
                  const fetchUrl = endpoint.startsWith('http') ? endpoint : getProxiedUrl(endpoint);
                  console.log(`Converted endpoint ${endpoint} to ${fetchUrl}`);

                  const response = await fetch(fetchUrl, {
                    headers: {
                      'Accept': 'application/json',
                      'Content-Type': 'application/json',
                      'Authorization': 'Basic ' + btoa('admin:admin123')
                    },
                    credentials: 'include'
                  });

                  if (response.ok) {
                    const data = await response.json();
                    console.log(`Successfully fetched ${entityType} via direct fetch:`, data);

                    if (Array.isArray(data)) {
                      result = data;
                      break;
                    } else if (data && typeof data === 'object') {
                      if (Array.isArray(data.content)) {
                        result = data.content;
                        break;
                      } else if (Array.isArray(data.items)) {
                        result = data.items;
                        break;
                      } else if (Array.isArray(data.data)) {
                        result = data.data;
                        break;
                      }
                    }
                  } else {
                    console.error(`Direct fetch for ${entityType} from ${endpoint} failed with status ${response.status}`);
                  }
                } catch (endpointError) {
                  console.error(`Direct fetch for ${entityType} from ${endpoint} failed:`, endpointError);
                }
              }

              if (Array.isArray(result) && result.length > 0) {
                console.log(`Successfully fetched ${entityType} data via direct fetch`);
              } else {
                // If all direct fetch attempts fail and fallbackToMockData is true, use mock data
                if (fallbackToMockData) {
                  console.warn(`All direct fetch attempts failed for ${entityType}, using mock data as fallback`);
                  result = [...mockDataMap[entityType]];
                } else {
                  throw new Error(`Failed to fetch ${entityType} data from any endpoint`);
                }
              }
            } catch (directFetchError) {
              console.error(`All direct fetch attempts for ${entityType} failed:`, directFetchError);

              // If all direct fetch attempts fail and fallbackToMockData is true, use mock data
              if (fallbackToMockData) {
                console.warn(`All direct fetch attempts failed for ${entityType}, using mock data as fallback`);
                result = [...mockDataMap[entityType]];
              } else {
                // If all attempts fail, re-throw the original error
                throw apiError;
              }
            }
          } else if (fallbackToMockData) {
            // For other entity types, if fallbackToMockData is true, use mock data
            console.warn(`No direct fetch available for ${entityType}, using mock data as fallback`);
            result = [...mockDataMap[entityType]];
          } else {
            // If all attempts fail, re-throw the original error
            throw apiError;
          }
        }
      }

      // Apply filters if any
      let filteredData = result;
      if (Object.keys(filters).length > 0) {
        filteredData = result.filter(item => {
          return Object.entries(filters).every(([key, value]) => {
            // Skip empty filter values
            if (value === undefined || value === null || value === '') {
              return true;
            }

            // Handle nested properties (e.g., 'client.name')
            if (key.includes('.')) {
              const parts = key.split('.');
              let nestedValue = item;
              for (const part of parts) {
                if (!nestedValue || !nestedValue[part]) {
                  return false;
                }
                nestedValue = nestedValue[part];
              }
              return String(nestedValue).toLowerCase().includes(String(value).toLowerCase());
            }

            // Handle direct properties
            return item[key] && String(item[key]).toLowerCase().includes(String(value).toLowerCase());
          });
        });
      }

      // Format the data if needed based on entity type
      let formattedData: any[] = [];

      if (entityType === 'projects') {
        // Special formatting for projects
        formattedData = filteredData.map(project => {
          // Create a properly formatted project object
          return {
            id: project.id || Math.random().toString(36).substring(2, 9),
            name: project.name || "Unnamed Project",
            client: typeof project.client === 'object' ? project.client :
                   project.clientId ? { id: project.clientId, name: project.clientName || `Client ${project.clientId}` } :
                   { id: 0, name: "Unknown Client" },
            description: project.description || "",
            startDate: project.startDate || new Date().toISOString(),
            endDate: project.endDate || new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(),
            status: project.status || "Not Started",
            value: project.value ? (project.value.toString().startsWith('₹') ? project.value : `₹${project.value}`) : "₹0.00",
            // Preserve other fields
            ...project
          };
        });
      } else if (entityType === 'bdms') {
        // Format BDM data
        formattedData = filteredData.map(item => ({
          id: item.id || Math.floor(Math.random() * 1000),
          name: item.name || `BDM #${item.id || 'Unknown'}`,
          email: item.email || null,
          phone: item.phone || null,
          gstNumber: item.gstNumber || item.gst_number || null,
          billingAddress: item.billingAddress || item.billing_address || null,
          commissionRate: item.commissionRate !== undefined && item.commissionRate !== null
            ? (typeof item.commissionRate === 'string'
                ? parseFloat(item.commissionRate)
                : item.commissionRate)
            : 0,
          notes: item.notes || null,
          clientCount: item.clientCount || item.client_count || 0,
          projectCount: item.projectCount || item.project_count || 0
        }));
      } else {
        // Default formatting for other entity types
        formattedData = filteredData.map(item => ({
          ...item,
          // Add any common formatting here
        }));
      }

      // Store in cache and update state
      const currentTime = Date.now();
      dataCache[cacheKey] = {
        data: formattedData,
        timestamp: currentTime
      };

      // STRICT PREVENTION: Update the last successful fetch time
      lastFetchTimes[cacheKey] = currentTime;

      setData(formattedData);
    } catch (err) {
      console.error(`Error fetching ${entityType}:`, err);
      setError(err instanceof Error ? err : new Error(String(err)));

      // Provide mock data as fallback in case of errors if allowed
      if (useMockData) {
        console.log(`Using mock data for ${entityType} due to error`);
        setData(mockDataMap[entityType] || []);
      } else {
        // If not using mock data, keep the data array empty
        setData([]);
      }

      // Show a more detailed error message
      if (err instanceof Error) {
        console.warn(`API Error Details: ${err.message}`);
      }
    } finally {
      // STRICT PREVENTION: Always release the lock when done
      requestLocks[cacheKey] = false;
      setLoading(false);
    }
  };

  // Use a ref to store filters to avoid unnecessary re-renders
  const filtersRef = React.useRef(filters);

  // Only update the ref if filters actually change in a meaningful way
  useEffect(() => {
    const oldFiltersStr = JSON.stringify(filtersRef.current);
    const newFiltersStr = JSON.stringify(filters);
    if (oldFiltersStr !== newFiltersStr) {
      filtersRef.current = filters;
    }
  }, [filters]);

  // Track if this component has already fetched data
  const hasFetchedRef = useRef(false);

  // Fetch data only once on mount or when entityType/refetchInterval changes
  useEffect(() => {
    // STRICT PREVENTION: Only fetch if we haven't already fetched for this entity type
    // or if the cache is empty
    const shouldFetch = !hasFetchedRef.current || !dataCache[cacheKey];

    if (shouldFetch) {
      console.log(`Initial data fetch for ${entityType}`);
      hasFetchedRef.current = true;
      fetchData();
    } else {
      console.log(`Skipping initial fetch for ${entityType} - already fetched or cached`);
      // If we have cached data, use it immediately
      if (dataCache[cacheKey]) {
        setData(dataCache[cacheKey].data);
        setLoading(false);
      }
    }

    // Set up automatic refetching if refetchInterval is provided
    // but only if it's a reasonable interval (at least 30 seconds)
    let intervalId: NodeJS.Timeout | null = null;
    if (refetchInterval > 0 && refetchInterval >= 30600) {
      console.log(`Setting up automatic refetching for ${entityType} every ${refetchInterval}ms`);
      intervalId = setInterval(() => {
        // Only refetch if not locked and minimum interval has passed
        const now = Date.now();
        const lastFetch = lastFetchTimes[cacheKey] || 0;
        if (!requestLocks[cacheKey] && (now - lastFetch) >= MIN_FETCH_INTERVAL) {
          console.log(`Auto-refetching ${entityType} data...`);
          fetchData();
        } else {
          console.log(`Skipping auto-refetch for ${entityType} - locked or too recent`);
        }
      }, refetchInterval);
    }

    // Clean up the interval when the component unmounts or when dependencies change
    return () => {
      if (intervalId) {
        console.log(`Clearing refetch interval for ${entityType}`);
        clearInterval(intervalId);
      }
    };
  }, [entityType, refetchInterval]);

  return { data, loading, error, refetch: fetchData };
}
