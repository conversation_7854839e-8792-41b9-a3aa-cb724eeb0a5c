package com.redberyl.invoiceapp.service.impl;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.redberyl.invoiceapp.dto.GstChallanDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.service.GstChallanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class GstChallanServiceImpl implements GstChallanService {

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private TemplateEngine templateEngine;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");

    @Override
    public GstChallanDto generateGstChallanData(String month) {
        try {
            System.out.println("=== Generating GST Challan Data for Month: " + month + " ===");

            // Parse the month to get date range
            LocalDate startDate, endDate;
            try {
                // Parse month like "January 2025"
                String[] parts = month.split(" ");
                if (parts.length == 2) {
                    int year = Integer.parseInt(parts[1]);
                    int monthNum = getMonthNumber(parts[0]);
                    startDate = LocalDate.of(year, monthNum, 1);
                    endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
                } else {
                    // Default to current month if parsing fails
                    LocalDate now = LocalDate.now();
                    startDate = now.withDayOfMonth(1);
                    endDate = now.withDayOfMonth(now.lengthOfMonth());
                }
            } catch (Exception e) {
                // Default to current month if parsing fails
                LocalDate now = LocalDate.now();
                startDate = now.withDayOfMonth(1);
                endDate = now.withDayOfMonth(now.lengthOfMonth());
            }

            // Get all invoices for the specified date range with all relations
            List<Invoice> invoices = invoiceRepository.findByInvoiceDateBetweenWithAllRelations(startDate, endDate);
            System.out.println("Found " + invoices.size() + " invoices for month: " + month);

            if (invoices.isEmpty()) {
                throw new RuntimeException("No invoices found for month: " + month);
            }

            return buildGstChallanDto(invoices, month);

        } catch (Exception e) {
            System.err.println("Error generating GST Challan data: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to generate GST Challan data: " + e.getMessage(), e);
        }
    }

    private int getMonthNumber(String monthName) {
        switch (monthName.toLowerCase()) {
            case "january": return 1;
            case "february": return 2;
            case "march": return 3;
            case "april": return 4;
            case "may": return 5;
            case "june": return 6;
            case "july": return 7;
            case "august": return 8;
            case "september": return 9;
            case "october": return 10;
            case "november": return 11;
            case "december": return 12;
            default: return LocalDate.now().getMonthValue();
        }
    }

    @Override
    public GstChallanDto generateGstChallanDataForDateRange(LocalDate startDate, LocalDate endDate) {
        try {
            System.out.println("=== Generating GST Challan Data for Date Range: " + startDate + " to " + endDate + " ===");

            // Get all invoices for the specified date range with all relations
            List<Invoice> invoices = invoiceRepository.findByInvoiceDateBetweenWithAllRelations(startDate, endDate);
            System.out.println("Found " + invoices.size() + " invoices for date range");

            if (invoices.isEmpty()) {
                throw new RuntimeException("No invoices found for the specified date range");
            }

            String monthRange = startDate.format(DATE_FORMATTER) + " to " + endDate.format(DATE_FORMATTER);
            return buildGstChallanDto(invoices, monthRange);

        } catch (Exception e) {
            System.err.println("Error generating GST Challan data for date range: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to generate GST Challan data for date range: " + e.getMessage(), e);
        }
    }

    @Override
    public GstChallanDto generateGstChallanDataForInvoices(List<Long> invoiceIds) {
        try {
            System.out.println("=== Generating GST Challan Data for Specific Invoices ===");
            System.out.println("Invoice IDs: " + invoiceIds);

            if (invoiceIds == null || invoiceIds.isEmpty()) {
                throw new RuntimeException("No invoice IDs provided");
            }

            // Get specific invoices by IDs with all relations
            List<Invoice> invoices = new ArrayList<>();
            List<Long> foundIds = new ArrayList<>();
            List<Long> notFoundIds = new ArrayList<>();

            for (Long invoiceId : invoiceIds) {
                System.out.println("Looking for invoice with ID: " + invoiceId);
                Optional<Invoice> invoiceOpt = invoiceRepository.findByIdWithAllRelations(invoiceId);
                if (invoiceOpt.isPresent()) {
                    invoices.add(invoiceOpt.get());
                    foundIds.add(invoiceId);
                    System.out.println("Found invoice: " + invoiceOpt.get().getInvoiceNumber());
                } else {
                    notFoundIds.add(invoiceId);
                    System.out.println("Invoice not found with ID: " + invoiceId);
                }
            }

            System.out.println("Found " + invoices.size() + " invoices for provided IDs");
            System.out.println("Found IDs: " + foundIds);
            System.out.println("Not found IDs: " + notFoundIds);

            if (invoices.isEmpty()) {
                throw new RuntimeException("No invoices found for the provided IDs: " + invoiceIds + ". Not found: " + notFoundIds);
            }

            if (!notFoundIds.isEmpty()) {
                System.out.println("Warning: Some invoice IDs were not found: " + notFoundIds);
            }

            // Create period description based on selected invoices' date range
            String period = generatePeriodFromInvoices(invoices);
            return buildGstChallanDto(invoices, period);

        } catch (Exception e) {
            System.err.println("Error generating GST Challan data for specific invoices: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to generate GST Challan data for specific invoices: " + e.getMessage(), e);
        }
    }

    private GstChallanDto buildGstChallanDto(List<Invoice> invoices, String period) {
        List<GstChallanDto.GstChallanEntryDto> entries = new ArrayList<>();
        BigDecimal totalBillAmount = BigDecimal.ZERO;
        BigDecimal totalCgst = BigDecimal.ZERO;
        BigDecimal totalSgst = BigDecimal.ZERO;
        BigDecimal totalIgst = BigDecimal.ZERO;
        BigDecimal totalInvoiceAmount = BigDecimal.ZERO;

        for (Invoice invoice : invoices) {
            GstChallanDto.GstChallanEntryDto entry = createChallanEntry(invoice);
            entries.add(entry);

            // Add to totals
            totalBillAmount = totalBillAmount.add(entry.getBillAmount());
            totalCgst = totalCgst.add(entry.getCgst());
            totalSgst = totalSgst.add(entry.getSgst());
            totalIgst = totalIgst.add(entry.getIgst());
            totalInvoiceAmount = totalInvoiceAmount.add(entry.getInvoiceAmount());
        }

        return GstChallanDto.builder()
                .challanNumber("GST-CHALLAN-" + System.currentTimeMillis())
                .challanDate(LocalDate.now())
                .month(period)
                .entries(entries)
                .totalBillAmount(totalBillAmount)
                .totalCgst(totalCgst)
                .totalSgst(totalSgst)
                .totalIgst(totalIgst)
                .totalInvoiceAmount(totalInvoiceAmount)
                .build();
    }

    private GstChallanDto.GstChallanEntryDto createChallanEntry(Invoice invoice) {
        // Calculate GST amounts
        BigDecimal billingAmount = invoice.getBillingAmount() != null ? invoice.getBillingAmount() : BigDecimal.ZERO;
        BigDecimal gstAmount = billingAmount.multiply(new BigDecimal("0.18"));
        
        // Determine if intra-state or inter-state
        boolean isIntraState = determineIfIntraState(invoice);
        
        BigDecimal cgstAmount = BigDecimal.ZERO;
        BigDecimal sgstAmount = BigDecimal.ZERO;
        BigDecimal igstAmount = BigDecimal.ZERO;
        
        if (isIntraState) {
            cgstAmount = gstAmount.divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);
            sgstAmount = gstAmount.divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);
        } else {
            igstAmount = gstAmount;
        }
        
        BigDecimal totalAmount = billingAmount.add(gstAmount);

        // Get candidate name and client information
        String candidateName = "";
        String clientName = "";
        String clientAddress = "";
        String clientGstNo = "";

        // Use candidate name as the primary name
        if (invoice.getCandidate() != null) {
            candidateName = invoice.getCandidate().getName();
        }

        // Get client information directly from invoice.client
        if (invoice.getClient() != null) {
            clientName = invoice.getClient().getName();
            clientAddress = invoice.getClient().getBillingAddress();
            clientGstNo = invoice.getClient().getGstNumber();
        }

        // If client info is not available from direct client, try to get it from project
        if (invoice.getProject() != null) {
            if (clientGstNo == null || clientGstNo.isEmpty()) {
                clientGstNo = invoice.getProject().getGstNumber();
            }

            // Try to get billing address from project first
            if (clientAddress == null || clientAddress.isEmpty()) {
                clientAddress = invoice.getProject().getBillingAddress();
            }

            // Also try to get client info from project.client if direct client is null or incomplete
            if (invoice.getProject().getClient() != null) {
                if (clientName == null || clientName.isEmpty()) {
                    clientName = invoice.getProject().getClient().getName();
                }
                if (clientAddress == null || clientAddress.isEmpty()) {
                    clientAddress = invoice.getProject().getClient().getBillingAddress();
                }
            }
        }

        // Calculate invoice month from invoice date
        String invoiceMonth = "";
        if (invoice.getInvoiceDate() != null) {
            invoiceMonth = invoice.getInvoiceDate().getMonth().name() + " " + invoice.getInvoiceDate().getYear();
        }

        // Ensure we have non-null values
        candidateName = candidateName != null ? candidateName : "";
        clientName = clientName != null ? clientName : "";
        clientAddress = clientAddress != null ? clientAddress : "";
        clientGstNo = clientGstNo != null ? clientGstNo : "";

        return GstChallanDto.GstChallanEntryDto.builder()
                .candidateName(candidateName)
                .clientName(clientName)
                .clientAddress(clientAddress)
                .clientGstNo(clientGstNo)
                .invoiceNo(invoice.getInvoiceNumber())
                .invoiceDate(invoice.getInvoiceDate())
                .invoiceMonth(invoiceMonth)
                .billAmount(billingAmount)
                .cgst(cgstAmount)
                .sgst(sgstAmount)
                .igst(igstAmount)
                .invoiceAmount(totalAmount)
                .build();
    }

    private boolean determineIfIntraState(Invoice invoice) {
        try {
            // Method 1: Check project state field directly
            if (invoice.getProject() != null && invoice.getProject().getState() != null) {
                String clientState = invoice.getProject().getState().trim();
                return clientState.equalsIgnoreCase("Maharashtra") ||
                       clientState.equalsIgnoreCase("MH") ||
                       clientState.equalsIgnoreCase("Maha");
            }

            // Method 2: Compare GST state codes (first 2 digits)
            String companyGstNumber = null;
            if (invoice.getRedberylAccount() != null && invoice.getRedberylAccount().getGstn() != null) {
                companyGstNumber = invoice.getRedberylAccount().getGstn();
            }

            String clientGstNumber = null;
            if (invoice.getProject() != null && invoice.getProject().getGstNumber() != null) {
                clientGstNumber = invoice.getProject().getGstNumber();
            }

            if (companyGstNumber != null && clientGstNumber != null &&
                companyGstNumber.length() >= 2 && clientGstNumber.length() >= 2) {

                String companyGstStateCode = companyGstNumber.substring(0, 2);
                String clientGstStateCode = clientGstNumber.substring(0, 2);
                return companyGstStateCode.equals(clientGstStateCode);
            }

            // Default to intra-state if we can't determine
            return true;
        } catch (Exception e) {
            System.err.println("Error determining state for GST calculation: " + e.getMessage());
            return true; // Default to intra-state
        }
    }

    @Override
    public Resource generateGstChallanPdf(GstChallanDto gstChallanDto) {
        try {
            String htmlContent = generateHtmlGstChallan(gstChallanDto);
            return convertHtmlToPdfResource(htmlContent);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate GST Challan PDF", e);
        }
    }

    @Override
    public Resource generateGstChallanPdfForMonth(String month) {
        GstChallanDto gstChallanDto = generateGstChallanData(month);
        return generateGstChallanPdf(gstChallanDto);
    }

    private String generateHtmlGstChallan(GstChallanDto gstChallanDto) {
        Context context = new Context();
        context.setVariable("challan", gstChallanDto);
        return templateEngine.process("gst-challan-template.html", context);
    }

    private Resource convertHtmlToPdfResource(String htmlContent) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            PdfWriter writer = new PdfWriter(outputStream);
            PdfDocument pdfDocument = new PdfDocument(writer);

            ConverterProperties converterProperties = new ConverterProperties();
            HtmlConverter.convertToPdf(htmlContent, pdfDocument, converterProperties);

            byte[] pdfBytes = outputStream.toByteArray();
            return new ByteArrayResource(pdfBytes);

        } catch (Exception e) {
            throw new RuntimeException("Failed to convert HTML to PDF", e);
        }
    }

    private String generatePeriodFromInvoices(List<Invoice> invoices) {
        if (invoices.isEmpty()) {
            return "No Invoices";
        }

        try {
            // Get all invoice dates
            List<LocalDate> invoiceDates = invoices.stream()
                .map(Invoice::getInvoiceDate)
                .filter(Objects::nonNull)
                .sorted()
                .collect(Collectors.toList());

            if (invoiceDates.isEmpty()) {
                return "Selected Invoices (" + invoices.size() + " invoices)";
            }

            LocalDate startDate = invoiceDates.get(0);
            LocalDate endDate = invoiceDates.get(invoiceDates.size() - 1);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");

            if (startDate.equals(endDate)) {
                // Single date
                return startDate.format(formatter);
            } else if (startDate.getMonth() == endDate.getMonth() && startDate.getYear() == endDate.getYear()) {
                // Same month and year
                return startDate.getMonth().getDisplayName(TextStyle.FULL, Locale.ENGLISH) + " " + startDate.getYear();
            } else {
                // Date range
                return startDate.format(formatter) + " to " + endDate.format(formatter);
            }
        } catch (Exception e) {
            System.err.println("Error generating period from invoices: " + e.getMessage());
            return "Selected Invoices (" + invoices.size() + " invoices)";
        }
    }
}
