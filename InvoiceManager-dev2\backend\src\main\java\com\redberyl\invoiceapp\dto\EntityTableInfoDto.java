package com.redberyl.invoiceapp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * DTO for entity table information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Entity table information")
public class EntityTableInfoDto {
    
    @Schema(description = "Table name")
    private String tableName;
    
    @Schema(description = "Table columns")
    private List<Map<String, Object>> columns;
    
    @Schema(description = "Primary keys")
    private List<String> primaryKeys;
    
    @Schema(description = "Foreign keys")
    private List<Map<String, Object>> foreignKeys;
}
