import { useEffect, useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { getApiBaseUrl } from "@/utils/ipUtils";

interface BackendStatusCheckerProps {
  backendUrl?: string;
  onStatusChange?: (isOnline: boolean) => void;
  hideUI?: boolean;
}

const BackendStatusChecker = ({
  backendUrl = "/api",
  onStatusChange,
  hideUI = false
}: BackendStatusCheckerProps) => {
  const [isOnline, setIsOnline] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkBackendStatus = async () => {
    // Don't check if we're already checking
    if (isChecking) return;

    setIsChecking(true);
    try {
      // Use a simple health check endpoint instead of auth/test/all
      const baseUrl = getApiBaseUrl();
      const response = await fetch(`${baseUrl}/actuator/health`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        // Set a short timeout
        signal: AbortSignal.timeout(3060),
        // Don't include credentials to avoid CORS issues
        credentials: 'omit'
      });

      // If we get here, the backend is reachable
      setIsOnline(true);
      if (onStatusChange) onStatusChange(true);
    } catch (error) {
      console.error("Backend status check failed:", error);
      setIsOnline(false);
      if (onStatusChange) onStatusChange(false);
    } finally {
      setIsChecking(false);
      setLastChecked(new Date());
    }
  };

  useEffect(() => {
    // Only check status if not hidden or if onStatusChange is provided
    if (!hideUI || onStatusChange) {
      // Check status on component mount
      checkBackendStatus();

      // Set up periodic checking only if not hidden
      const intervalId = !hideUI ? setInterval(checkBackendStatus, 60000) : null; // Check every 60 seconds instead of 30

      return () => {
        if (intervalId) clearInterval(intervalId);
      };
    }
  }, [backendUrl, hideUI, onStatusChange]);

  // If hideUI is true, don't render any UI but still perform status checking
  if (hideUI) {
    return null;
  }

  if (isOnline === null) {
    return (
      <Alert className="bg-gray-100 border-gray-300">
        <AlertTitle className="flex items-center">
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Checking backend connection...
        </AlertTitle>
      </Alert>
    );
  }

  if (isOnline) {
    return (
      <Alert className="bg-green-50 border-green-200">
        <AlertTitle className="text-green-700">Backend server is online</AlertTitle>
        <AlertDescription className="text-green-600">
          Connected to {backendUrl}
          {lastChecked && (
            <div className="text-xs mt-1">
              Last checked: {lastChecked.toLocaleTimeString()}
            </div>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Alert className="bg-red-50 border-red-200">
      <AlertTitle className="text-red-700">Backend server is offline</AlertTitle>
      <AlertDescription className="text-red-600">
        <p>Could not connect to {backendUrl}</p>
        <p className="mt-1">Please make sure the backend server is running on port 8091.</p>
        {lastChecked && (
          <div className="text-xs mt-1">
            Last checked: {lastChecked.toLocaleTimeString()}
          </div>
        )}
        <Button
          variant="outline"
          size="sm"
          className="mt-2 bg-white border-red-300 text-red-700 hover:bg-red-50"
          onClick={checkBackendStatus}
          disabled={isChecking}
        >
          {isChecking ? (
            <>
              <Loader2 className="h-3 w-3 mr-2 animate-spin" />
              Checking...
            </>
          ) : (
            'Check Again'
          )}
        </Button>
      </AlertDescription>
    </Alert>
  );
};

export default BackendStatusChecker;
