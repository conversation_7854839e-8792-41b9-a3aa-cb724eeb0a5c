package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.InvoiceTemplateConfigDto;
import com.redberyl.invoiceapp.entity.InvoiceTemplateConfig;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.repository.InvoiceTemplateConfigRepository;
import com.redberyl.invoiceapp.service.InvoiceTemplateConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Implementation of InvoiceTemplateConfigService
 */
@Service
@Transactional
public class InvoiceTemplateConfigServiceImpl implements InvoiceTemplateConfigService {

    @Autowired
    private InvoiceTemplateConfigRepository configRepository;

    @Override
    public List<InvoiceTemplateConfigDto> getAllConfigurations() {
        return configRepository.findByIsActiveTrueOrderByDisplayOrder()
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public InvoiceTemplateConfigDto getConfigurationById(Long id) {
        InvoiceTemplateConfig config = configRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Configuration not found with id: " + id));
        return convertToDto(config);
    }

    @Override
    public InvoiceTemplateConfigDto getConfigurationByKey(String configKey) {
        InvoiceTemplateConfig config = configRepository.findByConfigKey(configKey)
                .orElseThrow(() -> new ResourceNotFoundException("Configuration not found with key: " + configKey));
        return convertToDto(config);
    }

    @Override
    public List<InvoiceTemplateConfigDto> getConfigurationsByCategory(String category) {
        return configRepository.findByCategoryAndIsActiveTrueOrderByDisplayOrder(category)
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public String getConfigValue(String configKey) {
        return configRepository.findConfigValueByKey(configKey).orElse(null);
    }

    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        return configRepository.findConfigValueByKey(configKey).orElse(defaultValue);
    }

    @Override
    public Map<String, String> getAllConfigValues() {
        return configRepository.findByIsActiveTrueOrderByDisplayOrder()
                .stream()
                .collect(Collectors.toMap(
                        InvoiceTemplateConfig::getConfigKey,
                        config -> config.getConfigValue() != null ? config.getConfigValue() : ""
                ));
    }

    @Override
    public Map<String, String> getConfigValuesByCategory(String category) {
        return configRepository.findByCategoryAndIsActiveTrueOrderByDisplayOrder(category)
                .stream()
                .collect(Collectors.toMap(
                        InvoiceTemplateConfig::getConfigKey,
                        config -> config.getConfigValue() != null ? config.getConfigValue() : ""
                ));
    }

    @Override
    public InvoiceTemplateConfigDto createConfiguration(InvoiceTemplateConfigDto configDto) {
        // Check if key already exists
        if (configRepository.existsByConfigKey(configDto.getConfigKey())) {
            throw new IllegalArgumentException("Configuration with key '" + configDto.getConfigKey() + "' already exists");
        }

        InvoiceTemplateConfig config = convertToEntity(configDto);
        InvoiceTemplateConfig savedConfig = configRepository.save(config);
        return convertToDto(savedConfig);
    }

    @Override
    public InvoiceTemplateConfigDto updateConfiguration(Long id, InvoiceTemplateConfigDto configDto) {
        InvoiceTemplateConfig existingConfig = configRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Configuration not found with id: " + id));

        // Update fields
        existingConfig.setConfigValue(configDto.getConfigValue());
        existingConfig.setConfigType(configDto.getConfigType());
        existingConfig.setCategory(configDto.getCategory());
        existingConfig.setDescription(configDto.getDescription());
        existingConfig.setIsActive(configDto.getIsActive());
        existingConfig.setDisplayOrder(configDto.getDisplayOrder());

        InvoiceTemplateConfig savedConfig = configRepository.save(existingConfig);
        return convertToDto(savedConfig);
    }

    @Override
    public void deleteConfiguration(Long id) {
        if (!configRepository.existsById(id)) {
            throw new ResourceNotFoundException("Configuration not found with id: " + id);
        }
        configRepository.deleteById(id);
    }

    @Override
    public List<InvoiceTemplateConfigDto> bulkUpdateConfigurations(List<InvoiceTemplateConfigDto> configDtos) {
        return configDtos.stream()
                .map(dto -> {
                    if (dto.getId() != null) {
                        return updateConfiguration(dto.getId(), dto);
                    } else {
                        return createConfiguration(dto);
                    }
                })
                .collect(Collectors.toList());
    }

    @Override
    public void initializeDefaultConfigurations() {
        // Only initialize if no configurations exist
        if (configRepository.count() == 0) {
            System.out.println("Initializing default invoice template configurations...");

            // Company Information
            createDefaultConfig("company.name", "RedBeryl Tech Solutions", "TEXT", "COMPANY", "Company Name", 1);
            createDefaultConfig("company.address", "507-B Amanora Chambers", "TEXT", "COMPANY", "Company Address Line 1", 2);
            createDefaultConfig("company.city", "Amanora Mall, Hadapsar, Pune 411028", "TEXT", "COMPANY", "Company Address Line 2", 3);
            createDefaultConfig("company.phone", "+91 9876543210", "TEXT", "COMPANY", "Company Phone Number", 4);
            createDefaultConfig("company.email", "<EMAIL>", "TEXT", "COMPANY", "Company Email", 5);
            createDefaultConfig("company.website", "www.redberyl.com", "TEXT", "COMPANY", "Company Website", 6);
            createDefaultConfig("company.gstn", "27**********1Z5", "TEXT", "COMPANY", "Company GSTN", 7);
            createDefaultConfig("company.pan", "**********", "TEXT", "COMPANY", "Company PAN", 8);
            createDefaultConfig("company.cin", "U72200PN2020PTC123456", "TEXT", "COMPANY", "Company CIN", 9);

            // Bank Information
            createDefaultConfig("bank.name", "HDFC Bank", "TEXT", "BANK", "Bank Name", 10);
            createDefaultConfig("bank.branch", "Hadapsar Branch", "TEXT", "BANK", "Bank Branch", 11);
            createDefaultConfig("bank.account.name", "RedBeryl Tech Solutions", "TEXT", "BANK", "Account Holder Name", 12);
            createDefaultConfig("bank.account.number", "**************", "TEXT", "BANK", "Account Number", 13);
            createDefaultConfig("bank.ifsc", "HDFC0001234", "TEXT", "BANK", "IFSC Code", 14);
            createDefaultConfig("bank.account.type", "Current Account", "TEXT", "BANK", "Account Type", 15);

            // Template Settings
            createDefaultConfig("template.logo.url", "/images/logo.png", "TEXT", "TEMPLATE", "Logo URL", 16);
            createDefaultConfig("template.footer.text", "Thank you for your business!", "TEXT", "TEMPLATE", "Footer Text", 17);
            createDefaultConfig("template.terms.conditions", "Payment due within 30 days", "TEXT", "TEMPLATE", "Terms and Conditions", 18);
            createDefaultConfig("template.currency.symbol", "₹", "TEXT", "TEMPLATE", "Currency Symbol", 19);
            createDefaultConfig("template.date.format", "MM/dd/yyyy", "TEXT", "TEMPLATE", "Date Format", 20);

            System.out.println("Default invoice template configurations initialized successfully.");
        }
    }

    private void createDefaultConfig(String key, String value, String type, String category, String description, int order) {
        InvoiceTemplateConfig config = InvoiceTemplateConfig.builder()
                .configKey(key)
                .configValue(value)
                .configType(type)
                .category(category)
                .description(description)
                .isActive(true)
                .displayOrder(order)
                .build();
        configRepository.save(config);
    }

    private InvoiceTemplateConfigDto convertToDto(InvoiceTemplateConfig config) {
        return InvoiceTemplateConfigDto.builder()
                .id(config.getId())
                .configKey(config.getConfigKey())
                .configValue(config.getConfigValue())
                .configType(config.getConfigType())
                .category(config.getCategory())
                .description(config.getDescription())
                .isActive(config.getIsActive())
                .displayOrder(config.getDisplayOrder())
                .build();
    }

    private InvoiceTemplateConfig convertToEntity(InvoiceTemplateConfigDto dto) {
        return InvoiceTemplateConfig.builder()
                .id(dto.getId())
                .configKey(dto.getConfigKey())
                .configValue(dto.getConfigValue())
                .configType(dto.getConfigType())
                .category(dto.getCategory())
                .description(dto.getDescription())
                .isActive(dto.getIsActive())
                .displayOrder(dto.getDisplayOrder())
                .build();
    }
}
