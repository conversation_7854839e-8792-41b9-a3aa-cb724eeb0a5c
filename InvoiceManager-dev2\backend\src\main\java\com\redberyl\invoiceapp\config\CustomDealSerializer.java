package com.redberyl.invoiceapp.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.redberyl.invoiceapp.entity.Deal;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;

@JsonComponent
public class CustomDealSerializer extends JsonSerializer<Deal> {

    @Override
    public void serialize(Deal deal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        jsonGenerator.writeStartObject();
        
        // Write the deal properties
        jsonGenerator.writeNumberField("id", deal.getId());
        
        // Write the lead ID
        if (deal.getLead() != null) {
            jsonGenerator.writeNumberField("leadId", deal.getLead().getId());
        }
        
        // Write the client ID
        if (deal.getClient() != null) {
            jsonGenerator.writeNumberField("clientId", deal.getClient().getId());
            
            // Write the client name
            jsonGenerator.writeObjectFieldStart("client");
            jsonGenerator.writeNumberField("id", deal.getClient().getId());
            jsonGenerator.writeStringField("name", deal.getClient().getName());
            jsonGenerator.writeEndObject();
        }
        
        // Write the other deal properties
        jsonGenerator.writeStringField("projectName", deal.getProjectName());
        
        if (deal.getValueEstimate() != null) {
            jsonGenerator.writeNumberField("valueEstimate", deal.getValueEstimate());
        }
        
        if (deal.getExpectedClosureDate() != null) {
            jsonGenerator.writeStringField("expectedClosureDate", deal.getExpectedClosureDate().toString());
        }
        
        jsonGenerator.writeStringField("status", deal.getStatus());
        
        if (deal.getNotes() != null) {
            jsonGenerator.writeStringField("notes", deal.getNotes());
        }
        
        // Write the audit fields
        if (deal.getCreatedAt() != null) {
            jsonGenerator.writeStringField("created_at", deal.getCreatedAt().toString());
        }
        
        if (deal.getModifiedAt() != null) {
            jsonGenerator.writeStringField("updated_at", deal.getModifiedAt().toString());
        }
        
        jsonGenerator.writeEndObject();
    }
}

