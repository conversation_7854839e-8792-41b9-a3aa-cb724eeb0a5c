package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.InvoiceTaxDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.InvoiceTax;
import com.redberyl.invoiceapp.entity.TaxRate;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.InvoiceTaxRepository;
import com.redberyl.invoiceapp.repository.TaxRateRepository;
import com.redberyl.invoiceapp.service.InvoiceTaxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class InvoiceTaxServiceImpl implements InvoiceTaxService {

    @Autowired
    private InvoiceTaxRepository invoiceTaxRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private TaxRateRepository taxRateRepository;

    @Override
    public List<InvoiceTaxDto> getAllInvoiceTaxes() {
        List<InvoiceTax> invoiceTaxes = invoiceTaxRepository.findAll();
        if (invoiceTaxes.isEmpty()) {
            throw new NoContentException("No invoice taxes found");
        }
        return invoiceTaxes.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public InvoiceTaxDto getInvoiceTaxById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice tax ID cannot be null");
        }

        InvoiceTax invoiceTax = invoiceTaxRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice Tax not found with id: " + id));
        return convertToDto(invoiceTax);
    }

    @Override
    public List<InvoiceTaxDto> getInvoiceTaxesByInvoiceId(Long invoiceId) {
        if (invoiceId == null) {
            throw new NullConstraintViolationException("invoiceId", "Invoice ID cannot be null");
        }

        // Check if invoice exists
        if (!invoiceRepository.existsById(invoiceId)) {
            throw new ResourceNotFoundException("Invoice not found with id: " + invoiceId);
        }

        List<InvoiceTax> invoiceTaxes = invoiceTaxRepository.findByInvoiceId(invoiceId);
        if (invoiceTaxes.isEmpty()) {
            throw new NoContentException("No invoice taxes found for invoice with id: " + invoiceId);
        }

        return invoiceTaxes.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceTaxDto> getInvoiceTaxesByTaxRateId(Long taxRateId) {
        if (taxRateId == null) {
            throw new NullConstraintViolationException("taxRateId", "Tax rate ID cannot be null");
        }

        // Check if tax rate exists
        if (!taxRateRepository.existsById(taxRateId)) {
            throw new ResourceNotFoundException("Tax rate not found with id: " + taxRateId);
        }

        List<InvoiceTax> invoiceTaxes = invoiceTaxRepository.findByTaxRateId(taxRateId);
        if (invoiceTaxes.isEmpty()) {
            throw new NoContentException("No invoice taxes found for tax rate with id: " + taxRateId);
        }

        return invoiceTaxes.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateInvoiceTaxDto(InvoiceTaxDto invoiceTaxDto) {
        if (invoiceTaxDto == null) {
            throw new NullConstraintViolationException("invoiceTaxDto", "Invoice tax data cannot be null");
        }

        if (invoiceTaxDto.getInvoiceId() == null) {
            throw new NullConstraintViolationException("invoiceId", "Invoice ID cannot be null");
        }

        if (!invoiceRepository.existsById(invoiceTaxDto.getInvoiceId())) {
            throw new ForeignKeyViolationException("invoiceId",
                    "Invoice not found with id: " + invoiceTaxDto.getInvoiceId());
        }

        if (invoiceTaxDto.getTaxRateId() == null) {
            throw new NullConstraintViolationException("taxRateId", "Tax rate ID cannot be null");
        }

        if (!taxRateRepository.existsById(invoiceTaxDto.getTaxRateId())) {
            throw new ForeignKeyViolationException("taxRateId",
                    "Tax rate not found with id: " + invoiceTaxDto.getTaxRateId());
        }

        if (invoiceTaxDto.getAmount() == null) {
            throw new NullConstraintViolationException("amount", "Amount cannot be null");
        }
    }

    @Override
    @Transactional
    public InvoiceTaxDto createInvoiceTax(InvoiceTaxDto invoiceTaxDto) {
        validateInvoiceTaxDto(invoiceTaxDto);

        try {
            InvoiceTax invoiceTax = convertToEntity(invoiceTaxDto);
            InvoiceTax savedInvoiceTax = invoiceTaxRepository.save(invoiceTax);
            return convertToDto(savedInvoiceTax);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating invoice tax: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating invoice tax", e);
        }
    }

    @Override
    @Transactional
    public InvoiceTaxDto updateInvoiceTax(Long id, InvoiceTaxDto invoiceTaxDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice tax ID cannot be null");
        }

        if (invoiceTaxDto == null) {
            throw new NullConstraintViolationException("invoiceTaxDto", "Invoice tax data cannot be null");
        }

        InvoiceTax existingInvoiceTax = invoiceTaxRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice Tax not found with id: " + id));

        try {
            if (invoiceTaxDto.getInvoiceId() != null) {
                if (!invoiceRepository.existsById(invoiceTaxDto.getInvoiceId())) {
                    throw new ForeignKeyViolationException("invoiceId",
                            "Invoice not found with id: " + invoiceTaxDto.getInvoiceId());
                }

                Invoice invoice = invoiceRepository.findById(invoiceTaxDto.getInvoiceId()).get();
                existingInvoiceTax.setInvoice(invoice);
            }

            if (invoiceTaxDto.getTaxRateId() != null) {
                if (!taxRateRepository.existsById(invoiceTaxDto.getTaxRateId())) {
                    throw new ForeignKeyViolationException("taxRateId",
                            "Tax Rate not found with id: " + invoiceTaxDto.getTaxRateId());
                }

                TaxRate taxRate = taxRateRepository.findById(invoiceTaxDto.getTaxRateId()).get();
                existingInvoiceTax.setTaxRate(taxRate);
            }

            if (invoiceTaxDto.getAmount() != null) {
                existingInvoiceTax.setAmount(invoiceTaxDto.getAmount());
            }

            InvoiceTax updatedInvoiceTax = invoiceTaxRepository.save(existingInvoiceTax);
            return convertToDto(updatedInvoiceTax);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating invoice tax: " + e.getMessage(), e);
            }
        } catch (ResourceNotFoundException | NullConstraintViolationException | ForeignKeyViolationException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomException("Error updating invoice tax", e);
        }
    }

    @Override
    @Transactional
    public void deleteInvoiceTax(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice tax ID cannot be null");
        }

        if (!invoiceTaxRepository.existsById(id)) {
            throw new ResourceNotFoundException("Invoice Tax not found with id: " + id);
        }

        try {
            invoiceTaxRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete invoice tax because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting invoice tax: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting invoice tax", e);
        }
    }

    private InvoiceTaxDto convertToDto(InvoiceTax invoiceTax) {
        return InvoiceTaxDto.builder()
                .id(invoiceTax.getId())
                .invoiceId(invoiceTax.getInvoice() != null ? invoiceTax.getInvoice().getId() : null)
                .taxRateId(invoiceTax.getTaxRate() != null ? invoiceTax.getTaxRate().getId() : null)
                .amount(invoiceTax.getAmount())
                .build();
    }

    private InvoiceTax convertToEntity(InvoiceTaxDto invoiceTaxDto) {
        InvoiceTax invoiceTax = new InvoiceTax();
        invoiceTax.setId(invoiceTaxDto.getId());

        if (invoiceTaxDto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(invoiceTaxDto.getInvoiceId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Invoice not found with id: " + invoiceTaxDto.getInvoiceId()));
            invoiceTax.setInvoice(invoice);
        }

        if (invoiceTaxDto.getTaxRateId() != null) {
            TaxRate taxRate = taxRateRepository.findById(invoiceTaxDto.getTaxRateId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Tax Rate not found with id: " + invoiceTaxDto.getTaxRateId()));
            invoiceTax.setTaxRate(taxRate);
        }

        invoiceTax.setAmount(invoiceTaxDto.getAmount());

        return invoiceTax;
    }
}
