package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

/**
 * Entity for storing dynamic invoice template configuration values
 */
@Entity
@Table(name = "invoice_template_config")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InvoiceTemplateConfig extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "config_key", unique = true, nullable = false)
    private String configKey;

    @Column(name = "config_value", columnDefinition = "TEXT")
    private String configValue;

    @Column(name = "config_type")
    private String configType; // TEXT, NUMBER, BOOLEAN, JSON

    @Column(name = "category")
    private String category; // COMPANY, BANK, TEMPLATE, etc.

    @Column(name = "description")
    private String description;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "display_order")
    private Integer displayOrder;
}
