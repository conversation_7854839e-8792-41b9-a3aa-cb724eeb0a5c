@echo off
echo ===================================
echo IP Address Configuration for Windows
echo ===================================
echo.
echo This script will configure the IP address ************ on your network interface.
echo.
echo Please select your network interface:
echo 1. Ethernet
echo 2. Wi-Fi
echo 3. Other (you'll be prompted to enter the name)
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    set interface=Ethernet
) else if "%choice%"=="2" (
    set interface="Wi-Fi"
) else if "%choice%"=="3" (
    set /p interface="Enter your network interface name: "
) else (
    echo Invalid choice. Exiting.
    exit /b 1
)

echo.
echo You selected: %interface%
echo.
echo This will add the IP address ************ to your %interface% interface.
echo.
set /p confirm="Do you want to continue? (Y/N): "

if /i not "%confirm%"=="Y" (
    echo Operation cancelled. Exiting.
    exit /b 0
)

echo.
echo Adding IP address ************ to %interface%...
netsh interface ip add address %interface% ************ *************

if %ERRORLEVEL% neq 0 (
    echo.
    echo Failed to add IP address. Make sure you're running this script as Administrator.
    echo.
    pause
    exit /b 1
)

echo.
echo IP address ************ has been added to %interface%.
echo.
echo Verifying configuration...
ipconfig | findstr "************"

echo.
echo Configuration complete. You can now start the application.
echo.
pause
