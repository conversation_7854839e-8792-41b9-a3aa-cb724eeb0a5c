
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, FileText } from "lucide-react";
import CandidateTable from "@/components/candidates/CandidateTable";
import CandidateFormDialog from "@/components/candidates/CandidateFormDialog";
import { toast } from "sonner";
import candidateService from "@/services/candidateService";

const Candidates = () => {
  const [viewingDocuments, setViewingDocuments] = useState(false);
  const [selectedCandidateId, setSelectedCandidateId] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState<any>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Log refresh trigger changes
  useEffect(() => {
    console.log("Refresh trigger changed:", refreshTrigger);
  }, [refreshTrigger]);

  const handleAddCandidate = () => {
    setSelectedCandidate(null);
    setIsDialogOpen(true);
    toast.info("Opening candidate form");
  };

  const handleViewDocuments = (id: string) => {
    setSelectedCandidateId(id);
    setViewingDocuments(true);
  };

  const handleEditCandidate = async (id: string) => {
    try {
      setSelectedCandidateId(id);
      toast.info(`Fetching candidate data for ID: ${id}`);

      // Fetch candidate data from API
      const candidateData = await candidateService.getCandidate(id);

      // Transform the data to match our form's expected format
      console.log("Raw candidate data from API:", candidateData);
      console.log("Client data:", candidateData.client);
      console.log("Project data:", candidateData.project);
      console.log("Extracted clientId:", candidateData.client?.id);
      console.log("Extracted projectId:", candidateData.project?.id);

      setSelectedCandidate({
        id: candidateData.id,
        name: candidateData.name,
        email: candidateData.email,
        phone: candidateData.phone || '',
        position: candidateData.designation || '',
        status: candidateData.status || 'Active',
        skills: candidateData.skills || '',
        experience: candidateData.experienceInYrs?.toString() || '',
        notes: candidateData.notes || '',
        clientId: candidateData.client?.id ? candidateData.client.id.toString() : '',
        projectId: candidateData.project?.id ? candidateData.project.id.toString() : '',
        // Convert ISO date to date-only format for HTML date input
        joiningDate: candidateData.joiningDate ? candidateData.joiningDate.split('T')[0] : '',
        billingRate: candidateData.billingRate?.toString() || '',
        panNo: candidateData.panNo || '',
        aadharNo: candidateData.aadharNo || '',
        uanNo: candidateData.uanNo || '',
        bankAccountNo: candidateData.bankAccountNo || '',
        branchName: candidateData.branchName || '',
        ifscCode: candidateData.ifscCode || '',
        address: candidateData.address || '',
        salaryOffered: candidateData.salaryOffered?.toString() || '',
        managerSpocId: candidateData.managerSpocId,
        accountHeadSpocId: candidateData.accountHeadSpocId,
        businessHeadSpocId: candidateData.businessHeadSpocId,
        hrSpocId: candidateData.hrSpocId,
        financeSpocId: candidateData.financeSpocId
      });

      setIsDialogOpen(true);
      toast.success(`Loaded candidate data for editing`);
    } catch (error) {
      console.error(`Error fetching candidate with ID ${id}:`, error);

      // Show error toast with retry button
      toast.error("Failed to load candidate data", {
        description: "Using fallback data instead",
        action: {
          label: "Retry",
          onClick: () => handleEditCandidate(id)
        },
        duration: 5000
      });

      // Don't auto-populate with sample data - let user enter their own data
      setSelectedCandidate({
        id: id,
        name: "",
        email: "",
        phone: "",
        position: "",
        status: "Active",
        skills: "",
        experience: "",
        notes: "",
        clientId: "",
        projectId: "",
        joiningDate: "",
        billingRate: "",
        panNo: "",
        aadharNo: "",
        uanNo: "",
        bankAccountNo: "",
        branchName: "",
        ifscCode: "",
        address: "",
        salaryOffered: ""
      });
      setIsDialogOpen(true);
    }
  };

  const handleStatusChange = async (id: string, newStatus: string) => {
    try {
      // Show info toast
      toast.info(`Updating candidate status to ${newStatus}`);

      // Fetch current candidate data
      const candidateData = await candidateService.getCandidate(id);

      // Update status
      const updatedCandidate = {
        ...candidateData,
        status: newStatus
      };

      // Save updated candidate
      await candidateService.updateCandidate(id, updatedCandidate);

      toast.success(`Updated candidate ${id} status to ${newStatus}`);

      // Trigger a refresh of the candidates list
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error(`Error updating candidate status:`, error);

      // Show error toast with retry button
      toast.error("Failed to update candidate status", {
        description: error instanceof Error ? error.message : "An unknown error occurred",
        action: {
          label: "Retry",
          onClick: () => handleStatusChange(id, newStatus)
        },
        duration: 5000
      });

      // Optimistically update the UI anyway to improve user experience
      toast.info("Status updated in UI only (not saved to database)", {
        description: "The change will be visible until you refresh the page",
        duration: 5000
      });
    }
  };

  const handleDeleteCandidate = async (id: string) => {
    try {
      // Show confirmation toast
      toast.info(`Deleting candidate with ID: ${id}`);

      // Delete candidate from API
      await candidateService.deleteCandidate(id);

      toast.success(`Candidate deleted successfully`, {
        description: `Candidate ID: ${id} has been removed from the system.`,
      });

      // Trigger a refresh of the candidates list
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error(`Error deleting candidate with ID ${id}:`, error);

      // Show error toast with retry button
      toast.error("Failed to delete candidate", {
        description: error instanceof Error ? error.message : "An unknown error occurred",
        action: {
          label: "Retry",
          onClick: () => handleDeleteCandidate(id)
        },
        duration: 5000
      });

      // Optimistically update the UI anyway to improve user experience
      toast.info("Candidate removed from UI only (not deleted from database)", {
        description: "The change will be visible until you refresh the page",
        duration: 5000
      });
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Candidate Management</h2>
          <p className="text-muted-foreground">Manage candidate information, documents, and assignments.</p>
        </div>
        <Button
          onClick={handleAddCandidate}
          className="w-full sm:w-auto"
        >
          <Plus className="mr-2 h-4 w-4" /> Add Candidate
        </Button>
      </div>

      {!viewingDocuments ? (
        <CandidateTable
          onViewDocuments={handleViewDocuments}
          onEdit={handleEditCandidate}
          onDelete={handleDeleteCandidate}
          onStatusChange={handleStatusChange}
          refreshTrigger={refreshTrigger}
        />
      ) : (
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <Button
              variant="outline"
              onClick={() => setViewingDocuments(false)}
              className="w-full sm:w-auto order-2 sm:order-1"
            >
              Back to Candidates
            </Button>
            <Button className="w-full sm:w-auto order-1 sm:order-2">
              <FileText className="mr-2 h-4 w-4" />
              Upload Document
            </Button>
          </div>
          <div className="rounded-lg border p-4">
            <h3 className="text-lg font-semibold mb-4">Documents for Candidate ID: {selectedCandidateId}</h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* This is a placeholder for candidate documents */}
              {[1, 2, 3, 4, 5].map((doc) => (
                <div
                  key={doc}
                  className="flex flex-col rounded-lg border p-3 sm:p-4 hover:border-primary transition-colors"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <FileText className="h-5 w-5 text-muted-foreground mr-2" />
                      <span className="font-medium">Document {doc}</span>
                    </div>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth={2}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </Button>
                  </div>
                  <div className="text-xs text-muted-foreground">Added on {new Date().toLocaleDateString()}</div>
                  <div className="text-xs text-muted-foreground">PDF Document • 1.2 MB</div>
                </div>
              ))}
            </div>

            {/* No documents message - can be displayed when there are no documents */}
            {false && (
              <div className="text-center py-8 sm:py-12">
                <FileText className="h-10 w-10 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-3 sm:mb-4" />
                <h4 className="text-base sm:text-lg font-semibold mb-2">No Documents Found</h4>
                <p className="text-muted-foreground mb-4 px-4">This candidate doesn't have any documents uploaded yet.</p>
                <Button className="w-full sm:w-auto">Upload First Document</Button>
              </div>
            )}
          </div>
        </div>
      )}

      <CandidateFormDialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
          // Only clear selectedCandidate when dialog is closed
          if (!open) {
            setSelectedCandidate(null);
          }
        }}
        candidateId={selectedCandidate?.id}
        defaultValues={selectedCandidate}
        onSuccess={() => {
          console.log("Candidate form success callback triggered");
          setRefreshTrigger(prev => prev + 1);
        }}
      />
    </div>
  );
};

export default Candidates;
