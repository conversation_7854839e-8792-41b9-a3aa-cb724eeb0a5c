import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const AuthCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing authentication...');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const error = searchParams.get('error');
        const state = searchParams.get('state');

        if (error) {
          setStatus('error');
          setMessage(`Authentication failed: ${error}`);
          
          // Send error to parent window
          if (window.opener) {
            window.opener.postMessage({
              success: false,
              error: error
            }, '*');
            window.close();
          }
          return;
        }

        if (!code) {
          setStatus('error');
          setMessage('No authorization code received');
          
          // Send error to parent window
          if (window.opener) {
            window.opener.postMessage({
              success: false,
              error: 'No authorization code received'
            }, '*');
            window.close();
          }
          return;
        }

        // Forward the code to the backend for token exchange
        const response = await fetch('/api/onedrive/callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            code: code,
            state: state
          })
        });

        if (response.ok) {
          const data = await response.json();
          setStatus('success');
          setMessage('Authentication successful! You can close this window.');
          
          // Send success to parent window
          if (window.opener) {
            window.opener.postMessage({
              success: true,
              accessToken: data.accessToken || data.access_token
            }, '*');
            window.close();
          }
        } else {
          const errorData = await response.json();
          setStatus('error');
          setMessage(`Authentication failed: ${errorData.message || 'Unknown error'}`);
          
          // Send error to parent window
          if (window.opener) {
            window.opener.postMessage({
              success: false,
              error: errorData.message || 'Authentication failed'
            }, '*');
            window.close();
          }
        }
      } catch (error) {
        console.error('Error handling auth callback:', error);
        setStatus('error');
        setMessage(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        
        // Send error to parent window
        if (window.opener) {
          window.opener.postMessage({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }, '*');
          window.close();
        }
      }
    };

    handleCallback();
  }, [searchParams]);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f5f5f5'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '40px',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        textAlign: 'center',
        maxWidth: '400px'
      }}>
        {status === 'loading' && (
          <>
            <div style={{
              width: '40px',
              height: '40px',
              border: '4px solid #f3f3f3',
              borderTop: '4px solid #007bff',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 20px'
            }}></div>
            <h2 style={{ color: '#333', marginBottom: '10px' }}>Processing...</h2>
            <p style={{ color: '#666' }}>{message}</p>
          </>
        )}
        
        {status === 'success' && (
          <>
            <div style={{
              width: '40px',
              height: '40px',
              backgroundColor: '#28a745',
              borderRadius: '50%',
              margin: '0 auto 20px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '20px'
            }}>✓</div>
            <h2 style={{ color: '#28a745', marginBottom: '10px' }}>Success!</h2>
            <p style={{ color: '#666' }}>{message}</p>
          </>
        )}
        
        {status === 'error' && (
          <>
            <div style={{
              width: '40px',
              height: '40px',
              backgroundColor: '#dc3545',
              borderRadius: '50%',
              margin: '0 auto 20px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '20px'
            }}>✗</div>
            <h2 style={{ color: '#dc3545', marginBottom: '10px' }}>Error</h2>
            <p style={{ color: '#666' }}>{message}</p>
            <button 
              onClick={() => window.close()}
              style={{
                marginTop: '20px',
                padding: '10px 20px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Close Window
            </button>
          </>
        )}
      </div>
      
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default AuthCallback;
