package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.CandidateDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.CandidateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Public controller for candidates that doesn't require authentication
 * This is useful for testing and development purposes
 */
@RestController
@RequestMapping("/candidates")
@Tag(name = "Public Candidate API", description = "Public API for candidates (no authentication required)")
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"}, maxAge = 3600)
public class PublicCandidateController {

    @Autowired
    private CandidateService candidateService;

    @GetMapping
    @Operation(summary = "Get all candidates (public)", description = "Get all candidates without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Candidates found"),
            @ApiResponse(responseCode = "204", description = "No candidates found", content = @Content)
    })
    public ResponseEntity<List<CandidateDto>> getAllCandidates() {
        try {
            List<CandidateDto> candidates = candidateService.getAllCandidates();
            System.out.println("PublicCandidateController: Returning " + candidates.size() + " candidates");
            for (CandidateDto candidate : candidates) {
                System.out.println("Candidate: " + candidate.getName() + " (ID: " + candidate.getId() + ", Billing Rate: " + candidate.getBillingRate() + ")");
            }
            return new ResponseEntity<>(candidates, HttpStatus.OK);
        } catch (NoContentException e) {
            System.out.println("PublicCandidateController: No candidates found");
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            System.err.println("PublicCandidateController: Error fetching candidates: " + e.getMessage());
            e.printStackTrace();
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get candidate by ID (public)", description = "Get candidate by ID without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Candidate found"),
            @ApiResponse(responseCode = "404", description = "Candidate not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    public ResponseEntity<CandidateDto> getCandidateById(@PathVariable Long id) {
        try {
            CandidateDto candidate = candidateService.getCandidateById(id);
            System.out.println("PublicCandidateController: Returning candidate with ID: " + id);
            return new ResponseEntity<>(candidate, HttpStatus.OK);
        } catch (Exception e) {
            System.err.println("PublicCandidateController: Error fetching candidate with ID " + id + ": " + e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping
    @Operation(summary = "Create candidate (public)", description = "Create candidate without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Candidate created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    public ResponseEntity<CandidateDto> createCandidate(@Valid @RequestBody CandidateDto candidateDto) {
        try {
            CandidateDto createdCandidate = candidateService.createCandidate(candidateDto);
            System.out.println("PublicCandidateController: Created candidate with ID: " + createdCandidate.getId());
            return new ResponseEntity<>(createdCandidate, HttpStatus.CREATED);
        } catch (Exception e) {
            System.err.println("PublicCandidateController: Error creating candidate: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update candidate (public)", description = "Update candidate without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Candidate updated successfully"),
            @ApiResponse(responseCode = "404", description = "Candidate not found"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    public ResponseEntity<CandidateDto> updateCandidate(@PathVariable Long id, @Valid @RequestBody CandidateDto candidateDto) {
        try {
            CandidateDto updatedCandidate = candidateService.updateCandidate(id, candidateDto);
            System.out.println("PublicCandidateController: Updated candidate with ID: " + id);
            return new ResponseEntity<>(updatedCandidate, HttpStatus.OK);
        } catch (Exception e) {
            System.err.println("PublicCandidateController: Error updating candidate with ID " + id + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete candidate (public)", description = "Delete candidate without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Candidate deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Candidate not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    public ResponseEntity<Void> deleteCandidate(@PathVariable Long id) {
        try {
            candidateService.deleteCandidate(id);
            System.out.println("PublicCandidateController: Deleted candidate with ID: " + id);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            System.err.println("PublicCandidateController: Error deleting candidate with ID " + id + ": " + e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/sample")
    @Operation(summary = "Get sample candidates", description = "This endpoint no longer returns sample data")
    public ResponseEntity<List<CandidateDto>> getSampleCandidates() {
        System.out.println("Sample candidates endpoint called, returning empty list");
        // Return empty list instead of sample data
        return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
    }
}
