package com.redberyl.invoiceapp.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class BdmDto extends BaseDto {
    private Long id;

    @NotBlank(message = "BDM name is required")
    private String name;

    @Email(message = "Invalid email format")
    private String email;

    private String phone;
    private String gstNumber;
    private String billingAddress;

    @Min(value = 0, message = "Commission rate cannot be negative")
    @Max(value = 100, message = "Commission rate cannot exceed 100")
    @Builder.Default
    private BigDecimal commissionRate = BigDecimal.ZERO;

    private String notes;

    // For UI display purposes
    private Integer clientCount;
    private Integer projectCount;
}
