package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.ClientDto;
import com.redberyl.invoiceapp.dto.CommunicationDto;
import com.redberyl.invoiceapp.dto.LeadDto;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Communication;
import com.redberyl.invoiceapp.entity.Deal;
import com.redberyl.invoiceapp.entity.Lead;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.CommunicationRepository;
import com.redberyl.invoiceapp.repository.DealRepository;
import com.redberyl.invoiceapp.repository.LeadRepository;
import com.redberyl.invoiceapp.service.CommunicationService;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CommunicationServiceImpl implements CommunicationService {

    private final CommunicationRepository communicationRepository;
    private final ClientRepository clientRepository;
    private final LeadRepository leadRepository;
    private final DealRepository dealRepository;

    @Autowired
    public CommunicationServiceImpl(CommunicationRepository communicationRepository,
            ClientRepository clientRepository,
            LeadRepository leadRepository,
            DealRepository dealRepository) {
        this.communicationRepository = communicationRepository;
        this.clientRepository = clientRepository;
        this.leadRepository = leadRepository;
        this.dealRepository = dealRepository;
    }

    @Override
    public List<CommunicationDto> getAllCommunications() {
        List<Communication> communications = communicationRepository.findAll();
        if (communications.isEmpty()) {
            throw new NoContentException("No communications found");
        }
        return communications.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CommunicationDto getCommunicationById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Communication ID cannot be null");
        }

        Communication communication = communicationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Communication not found with id: " + id));
        return convertToDto(communication);
    }

    @Override
    public List<CommunicationDto> getCommunicationsByClientId(Long clientId) {
        if (clientId == null) {
            throw new NullConstraintViolationException("clientId", "Client ID cannot be null");
        }

        // Check if client exists
        if (!clientRepository.existsById(clientId)) {
            throw new ResourceNotFoundException("Client not found with id: " + clientId);
        }

        List<Communication> communications = communicationRepository.findByClientId(clientId);
        if (communications.isEmpty()) {
            throw new NoContentException("No communications found for client with id: " + clientId);
        }

        return communications.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CommunicationDto> getCommunicationsByLeadId(Long leadId) {
        if (leadId == null) {
            throw new NullConstraintViolationException("leadId", "Lead ID cannot be null");
        }

        // Check if lead exists
        if (!leadRepository.existsById(leadId)) {
            throw new ResourceNotFoundException("Lead not found with id: " + leadId);
        }

        List<Communication> communications = communicationRepository.findByLeadId(leadId);
        if (communications.isEmpty()) {
            throw new NoContentException("No communications found for lead with id: " + leadId);
        }

        return communications.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CommunicationDto> getCommunicationsByDealId(Long dealId) {
        if (dealId == null) {
            throw new NullConstraintViolationException("dealId", "Deal ID cannot be null");
        }

        // Check if deal exists
        if (!dealRepository.existsById(dealId)) {
            throw new ResourceNotFoundException("Deal not found with id: " + dealId);
        }

        List<Communication> communications = communicationRepository.findByDealId(dealId);
        if (communications.isEmpty()) {
            throw new NoContentException("No communications found for deal with id: " + dealId);
        }

        return communications.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateCommunicationDto(CommunicationDto communicationDto) {
        if (communicationDto == null) {
            throw new NullConstraintViolationException("communicationDto", "Communication data cannot be null");
        }

        if (!StringUtils.hasText(communicationDto.getSubject())) {
            throw new NullConstraintViolationException("subject", "Subject cannot be empty");
        }

        if (!StringUtils.hasText(communicationDto.getMethod())) {
            throw new NullConstraintViolationException("method", "Method cannot be empty");
        }

        // At least one of client, lead, or deal must be provided
        if (communicationDto.getClientId() == null &&
                communicationDto.getLeadId() == null &&
                communicationDto.getDealId() == null) {
            throw new NullConstraintViolationException("relationship",
                    "At least one of clientId, leadId, or dealId must be provided");
        }

        // Validate client ID if provided
        if (communicationDto.getClientId() != null && !clientRepository.existsById(communicationDto.getClientId())) {
            throw new ForeignKeyViolationException("clientId",
                    "Client not found with id: " + communicationDto.getClientId());
        }

        // Validate lead ID if provided
        if (communicationDto.getLeadId() != null && !leadRepository.existsById(communicationDto.getLeadId())) {
            throw new ForeignKeyViolationException("leadId",
                    "Lead not found with id: " + communicationDto.getLeadId());
        }

        // Validate deal ID if provided
        if (communicationDto.getDealId() != null && !dealRepository.existsById(communicationDto.getDealId())) {
            throw new ForeignKeyViolationException("dealId",
                    "Deal not found with id: " + communicationDto.getDealId());
        }
    }

    @Override
    @Transactional
    public CommunicationDto createCommunication(CommunicationDto communicationDto) {
        validateCommunicationDto(communicationDto);

        try {
            Communication communication = convertToEntity(communicationDto);
            Communication savedCommunication = communicationRepository.save(communication);
            return convertToDto(savedCommunication);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("field",
                        "Communication with these details already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating communication: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating communication", e);
        }
    }

    @Override
    @Transactional
    public CommunicationDto updateCommunication(Long id, CommunicationDto communicationDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Communication ID cannot be null");
        }

        Communication existingCommunication = communicationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Communication not found with id: " + id));

        // Validate the DTO fields that are being updated
        if (communicationDto.getClientId() != null && !clientRepository.existsById(communicationDto.getClientId())) {
            throw new ForeignKeyViolationException("clientId",
                    "Client not found with id: " + communicationDto.getClientId());
        }

        if (communicationDto.getLeadId() != null && !leadRepository.existsById(communicationDto.getLeadId())) {
            throw new ForeignKeyViolationException("leadId",
                    "Lead not found with id: " + communicationDto.getLeadId());
        }

        if (communicationDto.getDealId() != null && !dealRepository.existsById(communicationDto.getDealId())) {
            throw new ForeignKeyViolationException("dealId",
                    "Deal not found with id: " + communicationDto.getDealId());
        }

        try {
            updateCommunicationFromDto(existingCommunication, communicationDto);
            Communication updatedCommunication = communicationRepository.save(existingCommunication);
            return convertToDto(updatedCommunication);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("field",
                        "Communication with these details already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating communication: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating communication", e);
        }
    }

    @Override
    @Transactional
    public void deleteCommunication(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Communication ID cannot be null");
        }

        if (!communicationRepository.existsById(id)) {
            throw new ResourceNotFoundException("Communication not found with id: " + id);
        }

        try {
            communicationRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete communication because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting communication: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting communication", e);
        }
    }

    private CommunicationDto convertToDto(Communication communication) {
        CommunicationDto dto = new CommunicationDto();
        dto.setId(communication.getId());

        // Set relationship IDs
        if (communication.getClient() != null) {
            dto.setClientId(communication.getClient().getId());
        }

        if (communication.getLead() != null) {
            dto.setLeadId(communication.getLead().getId());
        }

        if (communication.getDeal() != null) {
            dto.setDealId(communication.getDeal().getId());
        }

        // Set other fields
        dto.setSubject(communication.getSubject());
        dto.setMethod(communication.getMethod());
        dto.setContent(communication.getContent());
        dto.setFollowUpDate(communication.getFollowUpDate());
        dto.setLoggedAt(communication.getLoggedAt());

        // Set client object
        if (communication.getClient() != null) {
            ClientDto clientDto = ClientDto.builder()
                    .id(communication.getClient().getId())
                    .name(communication.getClient().getName())
                    .build();

            // Set audit fields for client
            clientDto.setCreatedAt(communication.getClient().getCreatedAt());
            clientDto.setUpdatedAt(communication.getClient().getModifiedAt());

            dto.setClient(clientDto);
        }

        // Set lead object
        if (communication.getLead() != null) {
            LeadDto leadDto = LeadDto.builder()
                    .id(communication.getLead().getId())
                    .name(communication.getLead().getName())
                    .email(communication.getLead().getEmail())
                    .phone(communication.getLead().getPhone())
                    .build();

            // Set audit fields for lead
            leadDto.setCreatedAt(communication.getLead().getCreatedAt());
            leadDto.setUpdatedAt(communication.getLead().getModifiedAt());

            dto.setLead(leadDto);
        }

        // Deal relationship removed as requested - no DealDto field to set

        // Set audit fields
        dto.setCreatedAt(communication.getCreatedAt());
        dto.setUpdatedAt(communication.getModifiedAt());

        return dto;
    }

    private Communication convertToEntity(CommunicationDto communicationDto) {
        Communication communication = new Communication();
        communication.setId(communicationDto.getId());

        updateCommunicationFromDto(communication, communicationDto);

        return communication;
    }

    private void updateCommunicationFromDto(Communication communication, CommunicationDto communicationDto) {
        if (communicationDto.getClientId() != null) {
            Client client = clientRepository.findById(communicationDto.getClientId())
                    .orElseThrow(() -> new ForeignKeyViolationException("clientId",
                            "Client not found with id: " + communicationDto.getClientId()));
            communication.setClient(client);
        }

        if (communicationDto.getLeadId() != null) {
            Lead lead = leadRepository.findById(communicationDto.getLeadId())
                    .orElseThrow(() -> new ForeignKeyViolationException("leadId",
                            "Lead not found with id: " + communicationDto.getLeadId()));
            communication.setLead(lead);
        }

        if (communicationDto.getDealId() != null) {
            Deal deal = dealRepository.findById(communicationDto.getDealId())
                    .orElseThrow(() -> new ForeignKeyViolationException("dealId",
                            "Deal not found with id: " + communicationDto.getDealId()));
            communication.setDeal(deal);
        }

        if (StringUtils.hasText(communicationDto.getSubject())) {
            communication.setSubject(communicationDto.getSubject());
        }

        if (StringUtils.hasText(communicationDto.getMethod())) {
            communication.setMethod(communicationDto.getMethod());
        }

        communication.setContent(communicationDto.getContent());
        communication.setFollowUpDate(communicationDto.getFollowUpDate());
        communication.setLoggedAt(communicationDto.getLoggedAt());
    }
}
