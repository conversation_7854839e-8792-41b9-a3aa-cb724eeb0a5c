import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Divider,
  <PERSON><PERSON>,
  <PERSON>ner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useToast,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Code,
  Badge
} from '@chakra-ui/react';
import BdmDetails from '../BdmDetails';

const ClientWithBdmDetails = ({ clientId }) => {
  const toast = useToast();
  const [client, setClient] = useState(null);
  const [bdm, setBdm] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchClientWithBdm = async () => {
      if (!clientId) return;
      
      try {
        setLoading(true);
        setError(null);
        
        // Create basic auth header if needed
        const authHeader = 'Basic ' + btoa('admin:admin123');
        
        // Fetch client data
        const clientResponse = await fetch(`{import.meta.env.VITE_API_URL}/clients/${clientId}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          credentials: 'include'
        });
        
        if (!clientResponse.ok) {
          throw new Error(`Failed to fetch client: ${clientResponse.status}`);
        }
        
        const clientData = await clientResponse.json();
        console.log('Client data:', clientData);
        setClient(clientData);
        
        // If client has a BDM ID, fetch the BDM details
        if (clientData.bdmId) {
          const bdmResponse = await fetch(`http://localhost:8091/bdms/${clientData.bdmId}`, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            credentials: 'include'
          });
          
          if (!bdmResponse.ok) {
            console.warn(`Failed to fetch BDM details: ${bdmResponse.status}`);
            // Don't throw error here, just log a warning
          } else {
            const bdmResponseData = await bdmResponse.json();
            console.log('BDM response:', bdmResponseData);
            
            // Extract BDM data from the response
            let bdmData = null;
            if (bdmResponseData.success && bdmResponseData.data) {
              // Format from /api/v1/bdms/{id}
              bdmData = bdmResponseData.data;
            } else {
              // Direct format from /api/bdms/{id}
              bdmData = bdmResponseData;
            }
            
            console.log('Extracted BDM data:', bdmData);
            setBdm(bdmData);
          }
        }
      } catch (err) {
        console.error('Error fetching client with BDM:', err);
        setError(err.message);
        
        toast({
          title: 'Error fetching data',
          description: err.message,
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchClientWithBdm();
  }, [clientId, toast]);

  if (loading) {
    return (
      <Box textAlign="center" py={10}>
        <Spinner size="xl" />
        <Text mt={4}>Loading client and BDM data...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error">
        <AlertIcon />
        <AlertTitle>Error!</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!client) {
    return (
      <Alert status="info">
        <AlertIcon />
        <AlertTitle>No Client Found</AlertTitle>
        <AlertDescription>
          {clientId ? `Client with ID ${clientId} not found.` : 'No client ID provided.'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Box>
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
        {/* Client Information */}
        <Card>
          <CardHeader>
            <Heading size="md">Client Information</Heading>
          </CardHeader>
          <CardBody>
            <VStack align="stretch" spacing={3}>
              <HStack>
                <Text fontWeight="bold" width="150px">ID:</Text>
                <Text>{client.id}</Text>
              </HStack>
              
              <HStack>
                <Text fontWeight="bold" width="150px">Name:</Text>
                <Text>{client.name}</Text>
              </HStack>
              
              <HStack>
                <Text fontWeight="bold" width="150px">Email:</Text>
                <Text>{client.email}</Text>
              </HStack>
              
              <HStack>
                <Text fontWeight="bold" width="150px">Phone:</Text>
                <Text>{client.phone}</Text>
              </HStack>
              
              <HStack>
                <Text fontWeight="bold" width="150px">Contact Person:</Text>
                <Text>{client.contactPerson}</Text>
              </HStack>
              
              <HStack>
                <Text fontWeight="bold" width="150px">BDM ID:</Text>
                <Text>{client.bdmId || 'None'}</Text>
              </HStack>
              
              <HStack>
                <Text fontWeight="bold" width="150px">BDM Name:</Text>
                <Text>{client.bdmName || 'None'}</Text>
              </HStack>
              
              <HStack>
                <Text fontWeight="bold" width="150px">Commission %:</Text>
                <Text>{client.commissionPercentage || '0'}%</Text>
              </HStack>
            </VStack>
          </CardBody>
        </Card>
        
        {/* BDM Information */}
        <Card>
          <CardHeader>
            <Heading size="md">BDM Details</Heading>
          </CardHeader>
          <CardBody>
            {bdm ? (
              <BdmDetails bdm={bdm} showHeading={false} />
            ) : client.bdmId ? (
              <Alert status="warning">
                <AlertIcon />
                <AlertTitle>BDM Details Not Available</AlertTitle>
                <AlertDescription>
                  Could not fetch complete BDM details for ID: {client.bdmId}
                </AlertDescription>
              </Alert>
            ) : (
              <Alert status="info">
                <AlertIcon />
                <AlertTitle>No BDM Assigned</AlertTitle>
                <AlertDescription>
                  This client does not have a BDM assigned.
                </AlertDescription>
              </Alert>
            )}
          </CardBody>
        </Card>
      </SimpleGrid>
      
      {/* Raw Data */}
      <Card mt={6}>
        <CardHeader>
          <Heading size="md">Raw Data</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
            <Box>
              <Heading size="sm" mb={2}>Client Data</Heading>
              <Box overflowX="auto" maxH="300px" overflowY="auto">
                <Code p={2} display="block" whiteSpace="pre">
                  {JSON.stringify(client, null, 2)}
                </Code>
              </Box>
            </Box>
            
            <Box>
              <Heading size="sm" mb={2}>BDM Data</Heading>
              <Box overflowX="auto" maxH="300px" overflowY="auto">
                <Code p={2} display="block" whiteSpace="pre">
                  {JSON.stringify(bdm, null, 2)}
                </Code>
              </Box>
            </Box>
          </SimpleGrid>
        </CardBody>
      </Card>
    </Box>
  );
};

export default ClientWithBdmDetails;
