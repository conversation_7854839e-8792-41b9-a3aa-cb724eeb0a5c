package com.redberyl.invoiceapp.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.*;

/**
 * DTO for invoice template configuration
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class InvoiceTemplateConfigDto extends BaseDto {
    
    private Long id;
    
    @NotBlank(message = "Config key is required")
    private String configKey;
    
    private String configValue;
    
    private String configType; // TEXT, NUMBER, BOOLEAN, JSON
    
    private String category; // COMPANY, BANK, TEMPLATE, etc.
    
    private String description;
    
    private Boolean isActive = true;
    
    private Integer displayOrder;
}
