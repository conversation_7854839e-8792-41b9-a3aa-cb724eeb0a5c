package com.redberyl.invoiceapp.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.redberyl.invoiceapp.entity.Lead;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;

@JsonComponent
public class CustomLeadSerializer extends JsonSerializer<Lead> {

    @Override
    public void serialize(Lead lead, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        jsonGenerator.writeStartObject();
        
        // Write the lead properties
        jsonGenerator.writeNumberField("id", lead.getId());
        jsonGenerator.writeStringField("name", lead.getName());
        
        if (lead.getEmail() != null) {
            jsonGenerator.writeStringField("email", lead.getEmail());
        }
        
        if (lead.getPhone() != null) {
            jsonGenerator.writeStringField("phone", lead.getPhone());
        }
        
        if (lead.getSource() != null) {
            jsonGenerator.writeStringField("source", lead.getSource());
        }
        
        jsonGenerator.writeStringField("status", lead.getStatus());
        
        // Write the audit fields
        if (lead.getCreatedAt() != null) {
            jsonGenerator.writeStringField("created_at", lead.getCreatedAt().toString());
        }
        
        if (lead.getModifiedAt() != null) {
            jsonGenerator.writeStringField("updated_at", lead.getModifiedAt().toString());
        }
        
        jsonGenerator.writeEndObject();
    }
}

