import { getBasicAuthHeader } from '@/utils/apiUtils';

// Payment interfaces
export interface Payment {
  id?: number;
  invoiceId: number;
  amountReceived: number;
  receivedOn: string; // ISO date string
  paymentMode: string;
  referenceNumber?: string;
  // Additional fields from backend
  invoiceNumber?: string;
  clientName?: string;
}

export interface CreatePaymentRequest {
  invoiceId: number;
  amountReceived: number;
  receivedOn: string;
  paymentMode: string;
  referenceNumber?: string;
}

export interface PaymentResponse {
  id: number;
  invoiceId: number;
  amountReceived: number;
  receivedOn: string;
  paymentMode: string;
  referenceNumber?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Payment Service for handling payment-related API calls
 */
export const paymentService = {
  /**
   * Get all payments
   */
  getAllPayments: async (): Promise<Payment[]> => {
    try {
      console.log('PaymentService: Fetching all payments');

      // Try multiple endpoints
      const endpoints = [
        'http://localhost:8091/api/payments/getAll', // CORRECT ENDPOINT - matches backend exactly
      ];

      const authHeader = getBasicAuthHeader();

      for (const endpoint of endpoints) {
        try {
          console.log(`PaymentService: Trying endpoint ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            credentials: 'omit'
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`PaymentService: Successfully fetched payments from ${endpoint}:`, data);
            
            // Handle different response formats
            if (Array.isArray(data)) {
              return data;
            } else if (data && Array.isArray(data.data)) {
              return data.data;
            } else if (data && Array.isArray(data.content)) {
              return data.content;
            }
          } else {
            console.warn(`PaymentService: Endpoint ${endpoint} returned status ${response.status}`);
          }
        } catch (error) {
          console.error(`PaymentService: Error with endpoint ${endpoint}:`, error);
        }
      }

      console.warn('PaymentService: All endpoints failed, returning empty array');
      return [];
    } catch (error) {
      console.error('PaymentService: Error fetching payments:', error);
      return [];
    }
  },

  /**
   * Get payments by invoice ID
   */
  getPaymentsByInvoiceId: async (invoiceId: number): Promise<Payment[]> => {
    try {
      console.log(`PaymentService: Fetching payments for invoice ${invoiceId}`);

      const endpoints = [
        `http://localhost:8091/api/payments/getByInvoiceId/${invoiceId}`, // CORRECT ENDPOINT - matches backend exactly
      ];

      const authHeader = getBasicAuthHeader();

      for (const endpoint of endpoints) {
        try {
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            credentials: 'omit'
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`PaymentService: Successfully fetched payments for invoice ${invoiceId}:`, data);
            
            if (Array.isArray(data)) {
              return data;
            } else if (data && Array.isArray(data.data)) {
              return data.data;
            }
          }
        } catch (error) {
          console.error(`PaymentService: Error with endpoint ${endpoint}:`, error);
        }
      }

      return [];
    } catch (error) {
      console.error(`PaymentService: Error fetching payments for invoice ${invoiceId}:`, error);
      return [];
    }
  },

  /**
   * Create a new payment
   */
  createPayment: async (paymentData: CreatePaymentRequest): Promise<PaymentResponse> => {
    try {
      console.log('PaymentService: Creating payment:', paymentData);

      const endpoints = [
        'http://localhost:8091/api/payments/create', // CORRECT ENDPOINT - matches backend exactly
      ];

      const authHeader = getBasicAuthHeader();

      for (const endpoint of endpoints) {
        try {
          console.log(`PaymentService: Trying to create payment via ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(paymentData),
            credentials: 'omit'
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`PaymentService: Successfully created payment via ${endpoint}:`, data);
            return data;
          } else {
            const errorText = await response.text();
            console.warn(`PaymentService: Endpoint ${endpoint} returned status ${response.status}: ${errorText}`);
          }
        } catch (error) {
          console.error(`PaymentService: Error with endpoint ${endpoint}:`, error);
        }
      }

      throw new Error('Failed to create payment - all endpoints failed');
    } catch (error) {
      console.error('PaymentService: Error creating payment:', error);
      throw error;
    }
  },

  /**
   * Update a payment
   */
  updatePayment: async (id: number, paymentData: Partial<CreatePaymentRequest>): Promise<PaymentResponse> => {
    try {
      console.log(`PaymentService: Updating payment ${id}:`, paymentData);

      const endpoints = [
        `http://localhost:8091/api/payments/update/${id}`, // CORRECT ENDPOINT - matches backend exactly
      ];

      const authHeader = getBasicAuthHeader();

      for (const endpoint of endpoints) {
        try {
          const response = await fetch(endpoint, {
            method: 'PUT',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(paymentData),
            credentials: 'omit'
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`PaymentService: Successfully updated payment via ${endpoint}:`, data);
            return data;
          }
        } catch (error) {
          console.error(`PaymentService: Error with endpoint ${endpoint}:`, error);
        }
      }

      throw new Error('Failed to update payment - all endpoints failed');
    } catch (error) {
      console.error(`PaymentService: Error updating payment ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a payment
   */
  deletePayment: async (id: number): Promise<void> => {
    try {
      console.log(`PaymentService: Deleting payment ${id}`);

      const endpoints = [
        `http://localhost:8091/api/payments/deleteById/${id}`, // CORRECT ENDPOINT - matches backend exactly
      ];

      const authHeader = getBasicAuthHeader();

      for (const endpoint of endpoints) {
        try {
          const response = await fetch(endpoint, {
            method: 'DELETE',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            credentials: 'omit'
          });

          if (response.ok || response.status === 204) {
            console.log(`PaymentService: Successfully deleted payment via ${endpoint}`);
            return;
          }
        } catch (error) {
          console.error(`PaymentService: Error with endpoint ${endpoint}:`, error);
        }
      }

      throw new Error('Failed to delete payment - all endpoints failed');
    } catch (error) {
      console.error(`PaymentService: Error deleting payment ${id}:`, error);
      throw error;
    }
  }
};

export default paymentService;
