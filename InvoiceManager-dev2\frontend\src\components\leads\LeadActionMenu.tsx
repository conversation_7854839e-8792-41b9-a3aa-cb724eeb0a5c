import React, { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Edit, Users, Trash } from "lucide-react";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Dialog } from "@/components/ui/dialog";
import "./LeadActionMenu.css";
import { Lead as LeadType } from "@/services/leadService";

export type Lead = LeadType;

interface LeadActionMenuProps {
  lead: Lead;
  onEdit?: (lead: Lead) => void;
  onConvertToDeal?: (lead: Lead) => void;
  onDelete?: (leadId: string | number) => void;
}

const LeadActionMenu: React.FC<LeadActionMenuProps> = ({
  lead,
  onEdit,
  onConvertToDeal,
  onDelete
}) => {
  const [open, setOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isConvertDialogOpen, setIsConvertDialogOpen] = useState(false);

  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Close the dropdown menu
    setOpen(false);

    // Add a small delay to ensure the dropdown is closed before the action
    setTimeout(() => {
      try {
        if (onEdit) {
          // Call the parent component's edit handler
          onEdit(lead);
        } else {
          // Fallback if no handler is provided
          toast.info(`Edit lead: ${lead.name}`, {
            description: `Lead ID: ${lead.id}`,
          });
        }
      } catch (error) {
        console.error("Error in edit handler:", error);
        toast.error("Failed to edit lead. Please try again.");
      }
    }, 50); // Increased delay for better reliability
  };

  const handleConvertToDeal = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Close the dropdown menu
    setOpen(false);

    // Add a small delay to ensure the dropdown is closed before opening the dialog
    setTimeout(() => {
      try {
        setIsConvertDialogOpen(true);
      } catch (error) {
        console.error("Error opening convert dialog:", error);
        toast.error("Failed to open convert dialog. Please try again.");
      }
    }, 50); // Increased delay for better reliability
  };

  const confirmConvertToDeal = () => {
    try {
      if (onConvertToDeal) {
        onConvertToDeal(lead);
      } else {
        toast.success(`Lead converted to deal: ${lead.name}`, {
          description: `A new deal has been created from this lead.`,
        });
      }
    } catch (error) {
      console.error("Error in convert handler:", error);
      toast.error("Failed to convert lead to deal. Please try again.");
    } finally {
      setIsConvertDialogOpen(false);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Close the dropdown menu
    setOpen(false);

    // Add a small delay to ensure the dropdown is closed before opening the dialog
    setTimeout(() => {
      try {
        setIsDeleteDialogOpen(true);
      } catch (error) {
        console.error("Error opening delete dialog:", error);
        toast.error("Failed to open delete dialog. Please try again.");
      }
    }, 50); // Increased delay for better reliability
  };

  const confirmDelete = () => {
    try {
      if (onDelete) {
        onDelete(lead.id);
      } else {
        toast.success(`Lead deleted: ${lead.name}`, {
          description: `Lead ID: ${lead.id} has been removed.`,
        });
      }
    } catch (error) {
      console.error("Error in delete handler:", error);
      toast.error("Failed to delete lead. Please try again.");
    } finally {
      setIsDeleteDialogOpen(false);
    }
  };

  return (
    <>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[180px] p-2">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <div className="py-1">
            <DropdownMenuItem
              asChild
              onSelect={(e) => {
                e.preventDefault();
              }}
            >
              <button
                className="action-menu-item action-menu-item-edit w-full text-left"
                onClick={handleEdit}
                type="button"
              >
                <Edit className="action-menu-item-icon h-4 w-4" />
                <span className="action-menu-item-text">Edit</span>
              </button>
            </DropdownMenuItem>
          </div>

          <div className="py-1">
            <DropdownMenuItem
              asChild
              onSelect={(e) => {
                e.preventDefault();
              }}
            >
              <button
                className="action-menu-item action-menu-item-convert w-full text-left"
                onClick={handleConvertToDeal}
                type="button"
              >
                <Users className="action-menu-item-icon h-4 w-4" />
                <span className="action-menu-item-text">Convert to Deal</span>
              </button>
            </DropdownMenuItem>
          </div>

          <DropdownMenuSeparator />

          <div className="py-1">
            <DropdownMenuItem
              asChild
              onSelect={(e) => {
                e.preventDefault();
              }}
            >
              <button
                className="action-menu-item action-menu-item-delete w-full text-left"
                onClick={handleDelete}
                type="button"
              >
                <Trash className="action-menu-item-icon h-4 w-4" />
                <span className="action-menu-item-text">Delete</span>
              </button>
            </DropdownMenuItem>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this lead?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently remove {lead.name} from your leads list. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Convert to Deal Dialog */}
      <AlertDialog open={isConvertDialogOpen} onOpenChange={setIsConvertDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Convert Lead to Deal</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to convert {lead.name} from {lead.company} to a deal?
              This will create a new deal and mark this lead as converted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmConvertToDeal} className="bg-primary">
              Convert to Deal
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default LeadActionMenu;
