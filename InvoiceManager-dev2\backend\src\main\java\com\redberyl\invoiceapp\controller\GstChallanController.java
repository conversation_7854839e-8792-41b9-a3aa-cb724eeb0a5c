package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.GstChallanDto;
import com.redberyl.invoiceapp.service.GstChallanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/api/gst-challan")
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"})
@Tag(name = "GST Challan", description = "API for generating GST Challan reports")
public class GstChallanController {

    @Autowired
    private GstChallanService gstChallanService;

    /**
     * Generate GST Challan data for a specific month
     */
    @GetMapping("/data")
    @Operation(summary = "Generate GST Challan data", description = "Generate GST Challan data for a specific month")
    public ResponseEntity<GstChallanDto> generateGstChallanData(@RequestParam String month) {
        try {
            System.out.println("=== GST Challan Data Request ===");
            System.out.println("Requested Month: " + month);

            GstChallanDto gstChallanDto = gstChallanService.generateGstChallanData(month);
            
            System.out.println("Generated GST Challan with " + gstChallanDto.getEntries().size() + " entries");
            return ResponseEntity.ok(gstChallanDto);

        } catch (Exception e) {
            System.err.println("Error generating GST Challan data: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate GST Challan data for a date range
     */
    @GetMapping("/data/date-range")
    @Operation(summary = "Generate GST Challan data for date range", description = "Generate GST Challan data for a specific date range")
    public ResponseEntity<GstChallanDto> generateGstChallanDataForDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            System.out.println("=== GST Challan Data Request for Date Range ===");
            System.out.println("Start Date: " + startDate);
            System.out.println("End Date: " + endDate);

            GstChallanDto gstChallanDto = gstChallanService.generateGstChallanDataForDateRange(startDate, endDate);
            
            System.out.println("Generated GST Challan with " + gstChallanDto.getEntries().size() + " entries");
            return ResponseEntity.ok(gstChallanDto);

        } catch (Exception e) {
            System.err.println("Error generating GST Challan data for date range: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate GST Challan PDF for a specific month
     */
    @GetMapping("/pdf")
    @Operation(summary = "Generate GST Challan PDF", description = "Generate GST Challan PDF for a specific month")
    public ResponseEntity<Resource> generateGstChallanPdf(@RequestParam String month) {
        try {
            System.out.println("=== GST Challan PDF Request ===");
            System.out.println("Requested Month: " + month);

            Resource pdfResource = gstChallanService.generateGstChallanPdfForMonth(month);

            String filename = "GST_Challan_" + month.replace(" ", "_") + ".pdf";

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .body(pdfResource);

        } catch (Exception e) {
            System.err.println("Error generating GST Challan PDF: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate GST Challan PDF for a date range
     */
    @GetMapping("/pdf/date-range")
    @Operation(summary = "Generate GST Challan PDF for date range", description = "Generate GST Challan PDF for a specific date range")
    public ResponseEntity<Resource> generateGstChallanPdfForDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            System.out.println("=== GST Challan PDF Request for Date Range ===");
            System.out.println("Start Date: " + startDate);
            System.out.println("End Date: " + endDate);

            GstChallanDto gstChallanDto = gstChallanService.generateGstChallanDataForDateRange(startDate, endDate);
            Resource pdfResource = gstChallanService.generateGstChallanPdf(gstChallanDto);

            String filename = "GST_Challan_" + startDate + "_to_" + endDate + ".pdf";

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .body(pdfResource);

        } catch (Exception e) {
            System.err.println("Error generating GST Challan PDF for date range: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate GST Challan PDF for specific invoice IDs
     */


    @PostMapping("/pdf/invoices")
    @Operation(summary = "Generate GST Challan PDF for specific invoices", description = "Generate GST Challan PDF for specific invoice IDs")
    public ResponseEntity<Resource> generateGstChallanPdfForInvoices(@RequestBody java.util.List<Long> invoiceIds) {
        try {
            System.out.println("=== GST Challan PDF Request for Specific Invoices ===");
            System.out.println("Invoice IDs: " + invoiceIds);

            GstChallanDto gstChallanDto = gstChallanService.generateGstChallanDataForInvoices(invoiceIds);
            Resource pdfResource = gstChallanService.generateGstChallanPdf(gstChallanDto);

            String filename = "GST_Challan_Selected_Invoices.pdf";

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .body(pdfResource);

        } catch (Exception e) {
            System.err.println("Error generating GST Challan PDF for specific invoices: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate GST Challan PDF from provided data
     */
    @PostMapping("/pdf")
    @Operation(summary = "Generate GST Challan PDF from data", description = "Generate GST Challan PDF from provided challan data")
    public ResponseEntity<Resource> generateGstChallanPdfFromData(@RequestBody GstChallanDto gstChallanDto) {
        try {
            System.out.println("=== GST Challan PDF Request from Data ===");
            System.out.println("Entries count: " + gstChallanDto.getEntries().size());

            Resource pdfResource = gstChallanService.generateGstChallanPdf(gstChallanDto);

            String filename = "GST_Challan_" + gstChallanDto.getChallanNumber() + ".pdf";

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .body(pdfResource);

        } catch (Exception e) {
            System.err.println("Error generating GST Challan PDF from data: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
