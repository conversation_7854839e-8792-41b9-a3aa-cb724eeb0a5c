
import { useState, useEffect } from "react";
import candidateService, { Candidate } from "@/services/candidateService";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ChevronDown, Edit, MoreHorizontal, Trash, FileText, Search, Filter } from "lucide-react";
import { toast } from "sonner";
import CandidateActionMenu from "./CandidateActionMenu";
import CandidateStatusButton from "./CandidateStatusButton";

// This sample data is no longer used - we use sampleCandidatesData instead
// Keeping this for reference
const _unusedCandidatesData = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    designation: "Software Engineer",
    status: "Active",
    client: {
      id: "1",
      name: "Acme Corp"
    },
    project: {
      id: "1",
      name: "Mobile App Development"
    },
    joiningDate: "2023-01-15",
    billingRate: 75.00,
    panNo: "**********",
    aadharNo: "1234 5678 9012",
    uanNo: "************",
    experienceInYrs: 5.5,
    bankAccountNo: "**********",
    branchName: "Main Branch",
    ifscCode: "BANK0001234",
    address: "123 Main St, Anytown, AN 12345",
    salaryOffered: 85000.00,
    managerSpoc: {
      id: "1",
      name: "Sarah Johnson"
    },
    accountHeadSpoc: {
      id: "2",
      name: "Michael Brown"
    },
    skills: "JavaScript, React, Node.js"
  },
  {
    id: "2",
    name: "Jane Smith",
    email: "<EMAIL>",
    phone: "+****************",
    designation: "UI/UX Designer",
    status: "Inactive",
    client: {
      id: "2",
      name: "Globex Inc."
    },
    project: {
      id: "2",
      name: "Website Redesign"
    },
    joiningDate: "2022-08-10",
    billingRate: 80.00,
    panNo: "**********",
    aadharNo: "**************",
    uanNo: "************",
    experienceInYrs: 4.0,
    bankAccountNo: "**********",
    branchName: "Downtown Branch",
    ifscCode: "BANK0005678",
    address: "456 Oak St, Othertown, OT 67890",
    salaryOffered: 92000.00,
    managerSpoc: {
      id: "3",
      name: "Emily Davis"
    },
    hrSpoc: {
      id: "4",
      name: "David Wilson"
    },
    skills: "Figma, Adobe XD, Sketch, UI Design"
  },
  {
    id: "3",
    name: "Robert Wilson",
    email: "<EMAIL>",
    phone: "+****************",
    designation: "Project Manager",
    status: "Active",
    client: {
      id: "3",
      name: "Wayne Enterprises"
    },
    project: {
      id: "3",
      name: "ERP Implementation"
    },
    joiningDate: "2022-03-22",
    billingRate: 95.00,
    panNo: "**********",
    aadharNo: "**************",
    uanNo: "56**********",
    experienceInYrs: 8.5,
    bankAccountNo: "**********",
    branchName: "Financial District",
    ifscCode: "BANK0009012",
    address: "789 Pine St, Somewhere, SW 13579",
    salaryOffered: 110000.00,
    managerSpoc: {
      id: "5",
      name: "Jennifer Taylor"
    },
    businessHeadSpoc: {
      id: "6",
      name: "Robert Brown"
    },
    skills: "Project Management, Agile, Scrum, JIRA"
  },
  {
    id: "4",
    name: "Maria Garcia",
    email: "<EMAIL>",
    phone: "+****************",
    designation: "DevOps Engineer",
    status: "Pending",
    client: {
      id: "4",
      name: "Stark Industries"
    },
    project: {
      id: "4",
      name: "Cloud Migration"
    },
    joiningDate: "2023-05-05",
    billingRate: 85.00,
    panNo: "**********",
    aadharNo: "**************",
    uanNo: "************",
    experienceInYrs: 6.0,
    bankAccountNo: "**********",
    branchName: "Tech Center",
    ifscCode: "BANK0003456",
    address: "321 Elm St, Techville, TV 24680",
    salaryOffered: 95000.00,
    managerSpoc: {
      id: "7",
      name: "Thomas Anderson"
    },
    financeSpoc: {
      id: "8",
      name: "Lisa Wong"
    },
    skills: "AWS, Docker, Kubernetes, CI/CD"
  },
  {
    id: "5",
    name: "James Johnson",
    email: "<EMAIL>",
    phone: "+****************",
    designation: "Security Analyst",
    status: "Active",
    client: {
      id: "5",
      name: "Oscorp Industries"
    },
    project: {
      id: "5",
      name: "Security Audit"
    },
    joiningDate: "2022-11-15",
    billingRate: 90.00,
    panNo: "**********",
    aadharNo: "**************",
    uanNo: "************",
    experienceInYrs: 7.5,
    bankAccountNo: "**********",
    branchName: "Central Branch",
    ifscCode: "BANK0007890",
    address: "654 Maple St, Securetown, ST 97531",
    salaryOffered: 105000.00,
    managerSpoc: {
      id: "9",
      name: "Chris Evans"
    },
    accountHeadSpoc: {
      id: "10",
      name: "Natasha Romanoff"
    },
    skills: "Cybersecurity, Penetration Testing, SIEM"
  },
];

interface CandidateTableProps {
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  onViewDocuments?: (id: string) => void;
  onStatusChange?: (id: string, newStatus: string) => void;
  refreshTrigger?: number; // A number that changes to trigger a refresh
}

const CandidateTable = ({ onEdit, onDelete, onViewDocuments, onStatusChange, refreshTrigger = 0 }: CandidateTableProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCandidates, setFilteredCandidates] = useState<any[]>([]);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Create a local copy of the candidates data that we can modify
  const [candidates, setCandidates] = useState<any[]>([]);

  // Sample fallback data in case API fails
  const sampleCandidatesData = [
    {
      id: "1",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "+****************",
      designation: "Software Engineer",
      status: "Active",
      client: {
        id: "1",
        name: "Acme Corp"
      },
      project: {
        id: "1",
        name: "Mobile App Development"
      },
      joiningDate: "2023-01-15",
      billingRate: 75.00,
      panNo: "**********",
      aadharNo: "1234 5678 9012",
      uanNo: "************",
      experienceInYrs: 5.5,
      bankAccountNo: "**********",
      branchName: "Main Branch",
      ifscCode: "BANK0001234",
      address: "123 Main St, Anytown, AN 12345",
      salaryOffered: 85000.00,
      managerSpoc: {
        id: "1",
        name: "Sarah Johnson"
      },
      accountHeadSpoc: {
        id: "2",
        name: "Michael Brown"
      },
      skills: "JavaScript, React, Node.js"
    },
    {
      id: "2",
      name: "Jane Smith",
      email: "<EMAIL>",
      phone: "+****************",
      designation: "UI/UX Designer",
      status: "Active",
      client: {
        id: "2",
        name: "Globex Inc"
      },
      project: {
        id: "2",
        name: "Website Redesign"
      },
      joiningDate: "2023-02-20",
      billingRate: 80.00,
      panNo: "**********",
      aadharNo: "**************",
      uanNo: "************",
      experienceInYrs: 4.0,
      bankAccountNo: "**********",
      branchName: "Downtown Branch",
      ifscCode: "BANK0005678",
      address: "456 Oak St, Somewhere, SM 67890",
      salaryOffered: 90000.00,
      managerSpoc: {
        id: "1",
        name: "Sarah Johnson"
      },
      accountHeadSpoc: {
        id: "3",
        name: "David Wilson"
      },
      skills: "Figma, Adobe XD, UI/UX, Prototyping"
    }
  ];

  // Fetch candidates from API
  useEffect(() => {
    const fetchCandidates = async () => {
      try {
        setIsLoading(true);
        setError(null);
        console.log("Attempting to fetch candidates from API...", { refreshTrigger });

        // Force clear the candidates list before fetching to ensure we get fresh data
        setCandidates([]);
        setFilteredCandidates([]);

        // Try multiple endpoints in sequence
        const endpoints = [
          '/api/candidates/getAll',
          '/candidates/getAll',
          '/api/candidates',
          '/candidates',
          'http://localhost:8091/candidates/getAll'
        ];

        let success = false;
        let data = null;

        for (const endpoint of endpoints) {
          if (success) break;

          try {
            console.log(`Trying to fetch candidates from ${endpoint}`);
            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': 'Basic ' + btoa('admin:admin123')
              },
              credentials: 'include'
            });

            if (!response.ok) {
              console.error(`Fetch from ${endpoint} failed with status: ${response.status}`);
              continue;
            }

            data = await response.json();
            console.log(`Successfully fetched candidates from ${endpoint}:`, data);
            console.log("Number of candidates fetched:", data.length);
            success = true;
          } catch (endpointError) {
            console.error(`Error fetching from ${endpoint}:`, endpointError);
          }
        }

        // If all direct fetches failed, try using the service
        if (!success) {
          try {
            console.log("Trying to fetch candidates using candidateService");
            data = await candidateService.getAllCandidates();
            console.log("Successfully fetched candidates via service:", data);
            success = true;
          } catch (serviceError) {
            console.error("Service fetch failed:", serviceError);
          }
        }

        // If we have data, process it
        if (success && data) {
          // Transform the data to match our component's expected format
          const transformedData = data.map((candidate: any) => ({
            id: candidate.id?.toString() || "0",
            name: candidate.name || "Unnamed Candidate",
            email: candidate.email || "No email provided",
            phone: candidate.phone || "No phone provided",
            designation: candidate.designation || "No designation",
            status: candidate.status || 'Active',
            client: candidate.client || {
              id: candidate.clientId?.toString() || "0",
              name: candidate.clientId ? `Client ${candidate.clientId}` : "No client assigned"
            },
            project: candidate.project || {
              id: candidate.projectId?.toString() || "0",
              name: candidate.projectId ? `Project ${candidate.projectId}` : "No project assigned"
            },
            joiningDate: candidate.joiningDate || "Not specified",
            billingRate: candidate.billingRate || 0,
            experienceInYrs: candidate.experienceInYrs || 0,
            skills: candidate.skills || "Not specified"
          }));

          setCandidates(transformedData);
          setFilteredCandidates(transformedData);
          setIsLoading(false);
        } else {
          // If all attempts failed, use sample data
          console.error('All fetch attempts failed.');
          setError('Failed to load candidates from API.');

          // Don't use sample data - show empty state instead
          console.log("API failed - showing empty candidate list");
          setCandidates([]);
          setFilteredCandidates([]);

          // Show a toast notification
          toast.error("Failed to load candidates from API", {
            description: "Check network connection or API status.",
            duration: 5000,
          });
        }
      } catch (err) {
        console.error('Error in fetchCandidates:', err);
        setError('Failed to load candidates from API.');

        // Don't use sample data - show empty state instead
        console.log("API failed - showing empty candidate list");
        setCandidates([]);
        setFilteredCandidates([]);

        // Show a toast notification
        toast.error("Failed to load candidates from API", {
          description: err instanceof Error ? err.message : 'An unknown error occurred',
          duration: 5000,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCandidates();
  }, [refreshTrigger]); // Add refreshTrigger to dependency array to fetch data when it changes

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const term = e.target.value.toLowerCase();
      setSearchTerm(term);
      console.log(`Searching candidates for: "${term}"`);

      // Helper function to check if a candidate matches the search term
      const matchesSearchTerm = (candidate: any) => {
        return (
          (candidate.name || '').toLowerCase().includes(term) ||
          (candidate.email || '').toLowerCase().includes(term) ||
          ((candidate.client?.name || '') || '').toLowerCase().includes(term) ||
          ((candidate.project?.name || '') || '').toLowerCase().includes(term) ||
          (candidate.designation || '').toLowerCase().includes(term)
        );
      };

      const filtered = candidates.filter(matchesSearchTerm);

      if (statusFilter) {
        setFilteredCandidates(filtered.filter(c => c.status === statusFilter));
      } else {
        setFilteredCandidates(filtered);
      }
    } catch (error) {
      console.error('Error searching candidates:', error);
      // Don't show a toast here as it would be annoying during typing
      // Just log the error and show all candidates
      setFilteredCandidates(candidates);
    }
  };

  const filterByStatus = (status: string | null) => {
    try {
      setStatusFilter(status);
      console.log(`Filtering candidates by status: ${status || 'All'}`);

      // Helper function to check if a candidate matches the search term
      const matchesSearchTerm = (candidate: any) => {
        const term = searchTerm.toLowerCase();
        return (
          (candidate.name || '').toLowerCase().includes(term) ||
          (candidate.email || '').toLowerCase().includes(term) ||
          ((candidate.client?.name || '') || '').toLowerCase().includes(term) ||
          ((candidate.project?.name || '') || '').toLowerCase().includes(term) ||
          (candidate.designation || '').toLowerCase().includes(term)
        );
      };

      if (!status) {
        // If no status filter, just filter by search term
        const filtered = candidates.filter(matchesSearchTerm);
        setFilteredCandidates(filtered);
      } else {
        // Filter by both status and search term
        const filtered = candidates.filter(
          (candidate) =>
            candidate.status === status && matchesSearchTerm(candidate)
        );
        setFilteredCandidates(filtered);
      }
    } catch (error) {
      console.error('Error filtering candidates by status:', error);
      toast.error('Error filtering candidates', {
        description: 'Please try again or refresh the page',
      });

      // Reset to showing all candidates
      setStatusFilter(null);
      setFilteredCandidates(candidates);
    }
  };

  const handleEdit = (id: string) => {
    if (onEdit) {
      onEdit(id);
    } else {
      toast.info(`Edit candidate with ID: ${id}`);
    }
  };

  const handleDelete = (id: string) => {
    // Update the local candidates state by removing the deleted candidate
    const updatedCandidates = candidates.filter(candidate => candidate.id !== id);
    setCandidates(updatedCandidates);

    // Update the filtered candidates as well
    const updatedFilteredCandidates = filteredCandidates.filter(candidate => candidate.id !== id);
    setFilteredCandidates(updatedFilteredCandidates);

    // Call the parent handler if provided
    if (onDelete) {
      onDelete(id);
    } else {
      toast.success(`Deleted candidate with ID: ${id}`);
    }
  };

  const handleViewDocuments = (id: string) => {
    if (onViewDocuments) {
      onViewDocuments(id);
    } else {
      toast.info(`View documents for candidate with ID: ${id}`);
    }
  };

  const handleStatusChange = (id: string, newStatus: string) => {
    try {
      console.log(`Changing status for candidate ${id} to ${newStatus}`);

      // Update the local candidates state
      const updatedCandidates = candidates.map(candidate =>
        candidate.id === id ? { ...candidate, status: newStatus } : candidate
      );

      setCandidates(updatedCandidates);

      // Update the filtered candidates as well, respecting current filters
      let updatedFilteredCandidates: typeof filteredCandidates = [];

      if (statusFilter && statusFilter !== newStatus) {
        // If we're filtering by status and the new status doesn't match the filter,
        // remove the candidate from the filtered list
        updatedFilteredCandidates = filteredCandidates.filter(candidate => candidate.id !== id);
      } else {
        // Otherwise, update the status in the filtered list
        updatedFilteredCandidates = filteredCandidates.map(candidate =>
          candidate.id === id ? { ...candidate, status: newStatus } : candidate
        );
      }

      setFilteredCandidates(updatedFilteredCandidates);

      // Call the parent handler if provided
      if (onStatusChange) {
        try {
          onStatusChange(id, newStatus);
        } catch (callbackError) {
          console.error('Error in status change callback:', callbackError);
          toast.error('Error saving status change to server', {
            description: 'The status is updated in the UI but may not be saved to the database',
          });
        }
      } else {
        toast.success(`Status updated to ${newStatus} for candidate ID: ${id}`, {
          description: 'This change is only visible in the UI and not saved to the database',
        });
      }
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Failed to update status', {
        description: 'Please try again or refresh the page',
      });
    }
  };

  // Helper function to get status color classes (used by CandidateStatusButton component)
  // const getStatusColor = (status: string) => {
  //   switch (status.toLowerCase()) {
  //     case "active":
  //       return "bg-green-100 text-green-800 border-green-200";
  //     case "inactive":
  //       return "bg-red-100 text-red-800 border-red-200";
  //     case "pending":
  //       return "bg-yellow-100 text-yellow-800 border-yellow-200";
  //     default:
  //       return "bg-gray-100 text-gray-800 border-gray-200";
  //   }
  // };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Candidates</CardTitle>
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between mt-2">
          <div className="relative w-full sm:w-auto">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search candidates..."
              className="pl-8 w-full sm:w-[300px]"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
          <div className="flex items-center gap-2 w-full sm:w-auto">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-9 sm:h-8 w-full sm:w-auto">
                  <Filter className="mr-2 h-4 w-4" />
                  Filter
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => filterByStatus(null)}>
                  All
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => filterByStatus('Active')}>
                  Active
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => filterByStatus('Inactive')}>
                  Inactive
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => filterByStatus('Pending')}>
                  Pending
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border overflow-hidden">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="h-12 w-12 rounded-full border-4 border-primary/30 border-t-primary animate-spin mb-4"></div>
              <p className="text-muted-foreground">Loading candidates...</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-4 max-w-md text-center">
                <p className="font-semibold mb-2">Error loading candidates</p>
                <p className="text-sm">{error}</p>
                <p className="text-sm mt-2">Using sample data instead.</p>
              </div>
              <div className="flex gap-3">
                <Button
                  onClick={() => {
                    // Try multiple endpoints in sequence
                    setIsLoading(true);
                    setError(null);

                    const fetchCandidatesAgain = async () => {
                      const endpoints = [
                        '/api/candidates/getAll',
                        '/candidates/getAll',
                        '/api/candidates',
                        '/candidates',
                        'http://localhost:8091/candidates/getAll'
                      ];

                      let success = false;
                      let data = null;

                      for (const endpoint of endpoints) {
                        if (success) break;

                        try {
                          console.log(`Retrying to fetch candidates from ${endpoint}`);
                          const response = await fetch(endpoint, {
                            method: 'GET',
                            headers: {
                              'Accept': 'application/json',
                              'Content-Type': 'application/json',
                              'Authorization': 'Basic ' + btoa('admin:admin123')
                            },
                            credentials: 'include'
                          });

                          if (!response.ok) {
                            console.error(`Fetch from ${endpoint} failed with status: ${response.status}`);
                            continue;
                          }

                          data = await response.json();
                          console.log(`Successfully fetched candidates from ${endpoint}:`, data);
                          success = true;
                        } catch (endpointError) {
                          console.error(`Error fetching from ${endpoint}:`, endpointError);
                        }
                      }

                      if (success && data) {
                        // Transform the data to match our component's expected format
                        const transformedData = data.map((candidate: any) => ({
                          id: candidate.id?.toString() || "0",
                          name: candidate.name || "Unnamed Candidate",
                          email: candidate.email || "No email provided",
                          phone: candidate.phone || "No phone provided",
                          designation: candidate.designation || "No designation",
                          status: candidate.status || 'Active',
                          client: candidate.client || {
                            id: candidate.clientId?.toString() || "0",
                            name: candidate.clientId ? `Client ${candidate.clientId}` : "No client assigned"
                          },
                          project: candidate.project || {
                            id: candidate.projectId?.toString() || "0",
                            name: candidate.projectId ? `Project ${candidate.projectId}` : "No project assigned"
                          },
                          joiningDate: candidate.joiningDate || "Not specified",
                          billingRate: candidate.billingRate || 0,
                          experienceInYrs: candidate.experienceInYrs || 0,
                          skills: candidate.skills || "Not specified",
                          managerSpoc: candidate.managerSpoc || null,
                          salaryOffered: candidate.salaryOffered || 0
                        }));

                        setCandidates(transformedData);
                        setFilteredCandidates(transformedData);
                        toast.success("Successfully loaded candidate data");
                      } else {
                        setError('Failed to load candidates from API.');
                        setCandidates([]);
                        setFilteredCandidates([]);
                        toast.error("Failed to load candidates from API", {
                          description: "Check network connection or API status.",
                          duration: 5000,
                        });
                      }

                      setIsLoading(false);
                    };

                    fetchCandidatesAgain();
                  }}
                >
                  Retry Loading Data
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setError(null);
                    setCandidates([]);
                    setFilteredCandidates([]);
                    toast.success("Error cleared", {
                      description: "The error has been hidden. Try refreshing to reload data.",
                      duration: 3060
                    });
                  }}
                >
                  Clear Error
                </Button>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto overflow-y-hidden rounded-md">
              <Table className="min-w-[800px]">
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead className="hidden md:table-cell">Contact</TableHead>
                    <TableHead className="hidden lg:table-cell">Designation</TableHead>
                    <TableHead className="hidden lg:table-cell">Project</TableHead>
                    <TableHead className="hidden md:table-cell">Client</TableHead>
                    <TableHead className="hidden xl:table-cell">Manager</TableHead>
                    <TableHead className="hidden lg:table-cell">Billing</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCandidates.map((candidate) => (
                    <TableRow key={candidate.id}>
                      <TableCell className="font-medium">
                        <div className="flex flex-col">
                          <span>{candidate.name}</span>
                          <span className="md:hidden text-xs text-muted-foreground">{candidate.email}</span>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        <div className="flex flex-col">
                          <span className="text-sm">{candidate.email}</span>
                          <span className="text-xs text-muted-foreground">{candidate.phone}</span>
                        </div>
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">{candidate.designation || 'Not specified'}</TableCell>
                      <TableCell className="hidden lg:table-cell">{candidate.project?.name || 'Not assigned'}</TableCell>
                      <TableCell className="hidden md:table-cell">{candidate.client?.name || 'Not assigned'}</TableCell>
                      <TableCell className="hidden xl:table-cell">
                        {candidate.managerSpoc ? candidate.managerSpoc.name : "-"}
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        <div className="flex flex-col">
                          <span className="text-sm font-medium">₹{candidate.billingRate || 0}/month</span>
                          <span className="text-xs text-muted-foreground">
                            Salary: ₹{(candidate.salaryOffered || 0).toLocaleString('en-IN')}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <CandidateStatusButton
                          currentStatus={candidate.status}
                          onStatusChange={(newStatus) => handleStatusChange(candidate.id, newStatus)}
                        />
                      </TableCell>
                      <TableCell className="text-right">
                        <CandidateActionMenu
                          candidateId={candidate.id}
                          onEdit={handleEdit}
                          onViewDocuments={handleViewDocuments}
                          onDelete={handleDelete}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredCandidates.length === 0 && !isLoading && !error && (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-6 text-muted-foreground">
                        No candidates found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CandidateTable;
