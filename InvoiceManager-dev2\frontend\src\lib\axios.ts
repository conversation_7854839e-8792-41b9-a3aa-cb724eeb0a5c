import axios from 'axios';
import { getApiBaseUrl } from '@/utils/ipUtils';

// Create an axios instance with default configuration
export const api = axios.create({
  baseURL: getApiBaseUrl(),
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 10000 // 10 seconds
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    // Get token from localStorage
    const token = localStorage.getItem('token');

    // If token exists, add it to the headers
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle network errors
    if (!error.response) {
      console.error('Network error:', error.message);
      return Promise.reject(new Error('Network error. Please check your connection or if the server is running.'));
    }

    // Handle API errors
    const { status, data } = error.response;

    // Handle authentication errors
    if (status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('token');
      window.location.href = '/auth/login';
      return Promise.reject(new Error('Your session has expired. Please log in again.'));
    }

    // Return the error message from the API if available
    const errorMessage = data?.message || `Error ${status}: ${error.message}`;
    return Promise.reject(new Error(errorMessage));
  }
);
