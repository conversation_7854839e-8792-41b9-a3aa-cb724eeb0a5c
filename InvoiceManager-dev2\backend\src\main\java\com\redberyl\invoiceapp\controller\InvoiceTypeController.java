package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceTypeDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.InvoiceTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@CrossOrigin(origins = { "http://localhost:3060", "http://127.0.0.1:3060" }, maxAge = 3600)
@Tag(name = "Invoice Type", description = "Invoice Type management API")
public class InvoiceTypeController {

    @Autowired
    private InvoiceTypeService invoiceTypeService;

    @GetMapping("/invoice-types/getAll")
    @Operation(summary = "Get all invoice types", description = "Get all invoice types")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice types found"),
            @ApiResponse(responseCode = "204", description = "No invoice types found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceTypeDto>> getAllInvoiceTypes() {
        try {
            List<InvoiceTypeDto> invoiceTypes = invoiceTypeService.getAllInvoiceTypes();
            return new ResponseEntity<>(invoiceTypes, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoice-types/getById/{id}")
    @Operation(summary = "Get invoice type by ID", description = "Get invoice type by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice type found"),
            @ApiResponse(responseCode = "404", description = "Invoice type not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTypeDto> getInvoiceTypeById(@PathVariable Long id) {
        InvoiceTypeDto invoiceType = invoiceTypeService.getInvoiceTypeById(id);
        return new ResponseEntity<>(invoiceType, HttpStatus.OK);
    }

    @GetMapping("/invoice-types/getByType/{invoiceType}")
    @Operation(summary = "Get invoice type by type", description = "Get invoice type by type")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTypeDto> getInvoiceTypeByType(@PathVariable String invoiceType) {
        InvoiceTypeDto type = invoiceTypeService.getInvoiceTypeByType(invoiceType);
        return new ResponseEntity<>(type, HttpStatus.OK);
    }

    @PostMapping("/invoice-types/create")
    @Operation(summary = "Create invoice type", description = "Create invoice type")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTypeDto> createInvoiceType(@Valid @RequestBody InvoiceTypeDto invoiceTypeDto) {
        InvoiceTypeDto createdInvoiceType = invoiceTypeService.createInvoiceType(invoiceTypeDto);
        return new ResponseEntity<>(createdInvoiceType, HttpStatus.CREATED);
    }

    @PutMapping("/invoice-types/update/{id}")
    @Operation(summary = "Update invoice type", description = "Update invoice type")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTypeDto> updateInvoiceType(@PathVariable Long id,
            @Valid @RequestBody InvoiceTypeDto invoiceTypeDto) {
        InvoiceTypeDto updatedInvoiceType = invoiceTypeService.updateInvoiceType(id, invoiceTypeDto);
        return new ResponseEntity<>(updatedInvoiceType, HttpStatus.OK);
    }

    @DeleteMapping("/invoice-types/deleteById/{id}")
    @Operation(summary = "Delete invoice type", description = "Delete invoice type")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteInvoiceType(@PathVariable Long id) {
        invoiceTypeService.deleteInvoiceType(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


}
