<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Signup API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <h1>Test Signup API</h1>
    
    <div class="form-group">
        <label for="username">Username:</label>
        <input type="text" id="username" value="testuser">
    </div>
    
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="password123">
    </div>
    
    <div class="form-group">
        <label for="role">Role:</label>
        <select id="role">
            <option value="user">User</option>
            <option value="admin">Admin</option>
            <option value="mod">Moderator</option>
        </select>
    </div>
    
    <button onclick="testSignup()">Test Signup</button>
    
    <div id="result">
        <p>Results will appear here...</p>
    </div>
    
    <script>
        function testSignup() {
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const role = document.getElementById('role').value;
            
            // Add a random number to username and email to avoid duplicates
            const randomNum = Math.floor(Math.random() * 10000);
            const uniqueUsername = username + randomNum;
            const uniqueEmail = email.replace('@', randomNum + '@');
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Sending request...</p>';
            
            // Create the request payload
            const payload = {
                username: uniqueUsername,
                email: uniqueEmail,
                password: password,
                roles: [role]
            };
            
            console.log('Sending payload:', payload);
            
            // Send the request
            fetch('/api/auth/signup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload),
                credentials: 'include'
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                return response.json().then(data => {
                    return {
                        status: response.status,
                        statusText: response.statusText,
                        data: data
                    };
                });
            })
            .then(result => {
                console.log('Response data:', result);
                
                resultDiv.innerHTML = `
                    <h3>Response (${result.status} ${result.statusText})</h3>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    <p>Username: ${uniqueUsername}</p>
                    <p>Email: ${uniqueEmail}</p>
                `;
            })
            .catch(error => {
                console.error('Error:', error);
                
                resultDiv.innerHTML = `
                    <h3>Error</h3>
                    <pre>${error.message}</pre>
                `;
            });
        }
    </script>
</body>
</html>
