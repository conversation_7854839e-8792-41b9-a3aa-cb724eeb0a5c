package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.InvoiceTypeDto;
import com.redberyl.invoiceapp.entity.InvoiceType;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.InvoiceTypeRepository;
import com.redberyl.invoiceapp.service.InvoiceTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class InvoiceTypeServiceImpl implements InvoiceTypeService {

    @Autowired
    private InvoiceTypeRepository invoiceTypeRepository;

    @Override
    public List<InvoiceTypeDto> getAllInvoiceTypes() {
        List<InvoiceType> invoiceTypes = invoiceTypeRepository.findAll();
        if (invoiceTypes.isEmpty()) {
            throw new NoContentException("No invoice types found");
        }
        return invoiceTypes.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public InvoiceTypeDto getInvoiceTypeById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice type ID cannot be null");
        }

        InvoiceType invoiceType = invoiceTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice Type not found with id: " + id));
        return convertToDto(invoiceType);
    }

    @Override
    public InvoiceTypeDto getInvoiceTypeByType(String invoiceType) {
        if (!StringUtils.hasText(invoiceType)) {
            throw new NullConstraintViolationException("invoiceType", "Invoice type cannot be empty");
        }

        InvoiceType type = invoiceTypeRepository.findByInvoiceType(invoiceType)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice Type not found with type: " + invoiceType));
        return convertToDto(type);
    }

    private void validateInvoiceTypeDto(InvoiceTypeDto invoiceTypeDto) {
        if (invoiceTypeDto == null) {
            throw new NullConstraintViolationException("invoiceTypeDto", "Invoice type data cannot be null");
        }

        if (!StringUtils.hasText(invoiceTypeDto.getInvoiceType())) {
            throw new NullConstraintViolationException("invoiceType", "Invoice type cannot be empty");
        }

        // Check for duplicate invoice type if it's a new invoice type
        if (invoiceTypeDto.getId() == null &&
                invoiceTypeRepository.findByInvoiceType(invoiceTypeDto.getInvoiceType()).isPresent()) {
            throw new UniqueConstraintViolationException("invoiceType",
                    "Invoice type already exists: " + invoiceTypeDto.getInvoiceType());
        }
    }

    @Override
    @Transactional
    public InvoiceTypeDto createInvoiceType(InvoiceTypeDto invoiceTypeDto) {
        validateInvoiceTypeDto(invoiceTypeDto);

        try {
            InvoiceType invoiceType = convertToEntity(invoiceTypeDto);
            InvoiceType savedInvoiceType = invoiceTypeRepository.save(invoiceType);
            return convertToDto(savedInvoiceType);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("invoiceType", "Invoice type already exists");
            } else {
                throw new CustomException("Error creating invoice type: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating invoice type", e);
        }
    }

    @Override
    @Transactional
    public InvoiceTypeDto updateInvoiceType(Long id, InvoiceTypeDto invoiceTypeDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice type ID cannot be null");
        }

        if (invoiceTypeDto == null) {
            throw new NullConstraintViolationException("invoiceTypeDto", "Invoice type data cannot be null");
        }

        InvoiceType existingInvoiceType = invoiceTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice Type not found with id: " + id));

        // Check for duplicate invoice type if it's being changed
        if (StringUtils.hasText(invoiceTypeDto.getInvoiceType()) &&
                !invoiceTypeDto.getInvoiceType().equals(existingInvoiceType.getInvoiceType()) &&
                invoiceTypeRepository.findByInvoiceType(invoiceTypeDto.getInvoiceType()).isPresent()) {
            throw new UniqueConstraintViolationException("invoiceType",
                    "Invoice type already exists: " + invoiceTypeDto.getInvoiceType());
        }

        try {
            if (StringUtils.hasText(invoiceTypeDto.getInvoiceType())) {
                existingInvoiceType.setInvoiceType(invoiceTypeDto.getInvoiceType());
            }

            if (StringUtils.hasText(invoiceTypeDto.getTypeDesc())) {
                existingInvoiceType.setTypeDesc(invoiceTypeDto.getTypeDesc());
            }

            InvoiceType updatedInvoiceType = invoiceTypeRepository.save(existingInvoiceType);
            return convertToDto(updatedInvoiceType);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("invoiceType", "Invoice type already exists");
            } else {
                throw new CustomException("Error updating invoice type: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating invoice type", e);
        }
    }

    @Override
    @Transactional
    public void deleteInvoiceType(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice type ID cannot be null");
        }

        if (!invoiceTypeRepository.existsById(id)) {
            throw new ResourceNotFoundException("Invoice Type not found with id: " + id);
        }

        try {
            invoiceTypeRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete invoice type because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting invoice type: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting invoice type", e);
        }
    }

    private InvoiceTypeDto convertToDto(InvoiceType invoiceType) {
        return InvoiceTypeDto.builder()
                .id(invoiceType.getId())
                .invoiceType(invoiceType.getInvoiceType())
                .typeDesc(invoiceType.getTypeDesc())
                .build();
    }

    private InvoiceType convertToEntity(InvoiceTypeDto invoiceTypeDto) {
        return InvoiceType.builder()
                .id(invoiceTypeDto.getId())
                .invoiceType(invoiceTypeDto.getInvoiceType())
                .typeDesc(invoiceTypeDto.getTypeDesc())
                .build();
    }
}
