package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceTypeDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.InvoiceTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller for direct access to invoice types without any security constraints
 * This controller is specifically designed to ensure the frontend can always access invoice types
 */
@RestController
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"}, maxAge = 3600)
@Tag(name = "Direct Access Invoice Type", description = "Direct access to invoice types without authentication")
public class DirectAccessInvoiceTypeController {

    @Autowired
    private InvoiceTypeService invoiceTypeService;

    /**
     * Get all invoice types with direct access
     * @return List of invoice types
     */
    @GetMapping("/direct/invoice-types")
    @Operation(summary = "Get all invoice types (direct access)", description = "Get all invoice types with direct access")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice types found"),
            @ApiResponse(responseCode = "204", description = "No invoice types found", content = @Content)
    })
    public ResponseEntity<List<InvoiceTypeDto>> getAllInvoiceTypesDirect() {
        try {
            List<InvoiceTypeDto> invoiceTypes = invoiceTypeService.getAllInvoiceTypes();
            return new ResponseEntity<>(invoiceTypes, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    /**
     * Test endpoint that returns a fixed list of invoice types
     * This is useful for testing when the database is not available
     * @return Fixed list of invoice types
     */
    @GetMapping("/direct/invoice-types/test")
    @Operation(summary = "Test endpoint for invoice types (direct access)", description = "Simple test endpoint that returns a fixed list of invoice types")
    public ResponseEntity<List<InvoiceTypeDto>> getTestInvoiceTypesDirect() {
        // Create a fixed list of invoice types for testing
        List<InvoiceTypeDto> testTypes = List.of(
            InvoiceTypeDto.builder().id(1L).invoiceType("Standard").typeDesc("Regular invoice for services or products").build(),
            InvoiceTypeDto.builder().id(2L).invoiceType("Proforma").typeDesc("Preliminary bill of sale sent to buyers in advance of a shipment or delivery").build(),
            InvoiceTypeDto.builder().id(3L).invoiceType("Credit Note").typeDesc("Document issued to indicate a return of funds").build(),
            InvoiceTypeDto.builder().id(4L).invoiceType("Debit Note").typeDesc("Document issued to request additional payment").build(),
            InvoiceTypeDto.builder().id(5L).invoiceType("Tax Invoice").typeDesc("Invoice that includes tax information").build()
        );

        return new ResponseEntity<>(testTypes, HttpStatus.OK);
    }
}
