import React from "react";
import { Download, Edit, File<PERSON>lock, Trash } from "lucide-react";
import { toast } from "sonner";

interface StandaloneActionMenuProps {
  document: any;
  onDownload?: (document: any) => void;
  onEdit?: (document: any) => void;
  onViewHistory?: (document: any) => void;
  onDelete?: (documentId: string) => void;
  onClose?: () => void;
}

const StandaloneActionMenu: React.FC<StandaloneActionMenuProps> = ({
  document,
  onDownload,
  onEdit,
  onViewHistory,
  onDelete,
  onClose,
}) => {
  const handleDownload = () => {
    if (onDownload) {
      onDownload(document);
    } else {
      toast.success(`Downloading ${document.name}`, {
        description: `File type: ${document.type}`,
      });
    }
    if (onClose) onClose();
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(document);
    } else {
      toast.info(`Editing ${document.name}`, {
        description: "Edit functionality would open a form in a real app",
      });
    }
    if (onClose) onClose();
  };

  const handleViewHistory = () => {
    if (onViewHistory) {
      onViewHistory(document);
    } else {
      toast.info(`Viewing version history for ${document.name}`, {
        description: "Version history would be displayed in a real app",
      });
    }
    if (onClose) onClose();
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(document.id);
    } else {
      toast.success(`Deleted ${document.name}`, {
        description: "Document deleted successfully",
      });
    }
    if (onClose) onClose();
  };

  return (
    <div
      className="bg-white shadow-lg rounded-md border p-2 min-w-[180px] action-menu"
      data-menu-id={document.id}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="text-sm font-medium mb-2 px-2">Actions</div>
      <div className="flex flex-col">
        <button
          className="flex items-center text-sm hover:bg-gray-100 p-2 rounded w-full text-left"
          onClick={handleDownload}
        >
          <Download className="h-4 w-4 mr-2" />
          Download
        </button>

        <button
          className="flex items-center text-sm hover:bg-gray-100 p-2 rounded w-full text-left"
          onClick={handleEdit}
        >
          <Edit className="h-4 w-4 mr-2" />
          Edit Variables
        </button>

        <button
          className="flex items-center text-sm hover:bg-gray-100 p-2 rounded w-full text-left"
          onClick={handleViewHistory}
        >
          <FileClock className="h-4 w-4 mr-2" />
          Version History
        </button>

        <button
          className="flex items-center text-sm text-red-600 hover:bg-red-50 p-2 rounded w-full text-left"
          onClick={handleDelete}
        >
          <Trash className="h-4 w-4 mr-2" />
          Delete
        </button>
      </div>
    </div>
  );
};

export default StandaloneActionMenu;
