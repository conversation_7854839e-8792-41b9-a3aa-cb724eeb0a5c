package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.GeneratedDocumentDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.GeneratedDocumentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "Generated Document", description = "Generated Document management API")
public class GeneratedDocumentController {

    @Autowired
    private GeneratedDocumentService generatedDocumentService;

    @GetMapping("/generated-documents/getAll")
    @Operation(summary = "Get all generated documents", description = "Get all generated documents")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Generated documents found"),
            @ApiResponse(responseCode = "204", description = "No generated documents found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<GeneratedDocumentDto>> getAllGeneratedDocuments() {
        try {
            List<GeneratedDocumentDto> generatedDocuments = generatedDocumentService.getAllGeneratedDocuments();
            return new ResponseEntity<>(generatedDocuments, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/generated-documents/getById/{id}")
    @Operation(summary = "Get generated document by ID", description = "Get generated document by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Generated document found"),
            @ApiResponse(responseCode = "404", description = "Generated document not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<GeneratedDocumentDto> getGeneratedDocumentById(@PathVariable Long id) {
        GeneratedDocumentDto generatedDocument = generatedDocumentService.getGeneratedDocumentById(id);
        return new ResponseEntity<>(generatedDocument, HttpStatus.OK);
    }

    @GetMapping("/generated-documents/getByTemplateId/{templateId}")
    @Operation(summary = "Get generated documents by template ID", description = "Get generated documents by template ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Generated documents found"),
            @ApiResponse(responseCode = "204", description = "No generated documents found for this template"),
            @ApiResponse(responseCode = "404", description = "Template not found"),
            @ApiResponse(responseCode = "400", description = "Invalid template ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<GeneratedDocumentDto>> getGeneratedDocumentsByTemplateId(@PathVariable Long templateId) {
        try {
            List<GeneratedDocumentDto> generatedDocuments = generatedDocumentService
                    .getGeneratedDocumentsByTemplateId(templateId);
            return new ResponseEntity<>(generatedDocuments, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/generated-documents/getByClientId/{clientId}")
    @Operation(summary = "Get generated documents by client ID", description = "Get generated documents by client ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Generated documents found"),
            @ApiResponse(responseCode = "204", description = "No generated documents found for this client"),
            @ApiResponse(responseCode = "404", description = "Client not found"),
            @ApiResponse(responseCode = "400", description = "Invalid client ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<GeneratedDocumentDto>> getGeneratedDocumentsByClientId(@PathVariable Long clientId) {
        try {
            List<GeneratedDocumentDto> generatedDocuments = generatedDocumentService
                    .getGeneratedDocumentsByClientId(clientId);
            return new ResponseEntity<>(generatedDocuments, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/generated-documents/getByDealId/{dealId}")
    @Operation(summary = "Get generated documents by deal ID", description = "Get generated documents by deal ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Generated documents found"),
            @ApiResponse(responseCode = "204", description = "No generated documents found for this deal"),
            @ApiResponse(responseCode = "404", description = "Deal not found"),
            @ApiResponse(responseCode = "400", description = "Invalid deal ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<GeneratedDocumentDto>> getGeneratedDocumentsByDealId(@PathVariable Long dealId) {
        try {
            List<GeneratedDocumentDto> generatedDocuments = generatedDocumentService
                    .getGeneratedDocumentsByDealId(dealId);
            return new ResponseEntity<>(generatedDocuments, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/generated-documents/getByStatus/{status}")
    @Operation(summary = "Get generated documents by status", description = "Get generated documents by status")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Generated documents found"),
            @ApiResponse(responseCode = "204", description = "No generated documents found with this status"),
            @ApiResponse(responseCode = "400", description = "Invalid status supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<GeneratedDocumentDto>> getGeneratedDocumentsByStatus(@PathVariable String status) {
        try {
            List<GeneratedDocumentDto> generatedDocuments = generatedDocumentService
                    .getGeneratedDocumentsByStatus(status);
            return new ResponseEntity<>(generatedDocuments, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/generated-documents/create")
    @Operation(summary = "Create generated document", description = "Create generated document")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Generated document created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<GeneratedDocumentDto> createGeneratedDocument(
            @Valid @RequestBody GeneratedDocumentDto generatedDocumentDto) {
        GeneratedDocumentDto createdGeneratedDocument = generatedDocumentService
                .createGeneratedDocument(generatedDocumentDto);
        return new ResponseEntity<>(createdGeneratedDocument, HttpStatus.CREATED);
    }

    @PutMapping("/generated-documents/update/{id}")
    @Operation(summary = "Update generated document", description = "Update generated document")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Generated document updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "Generated document not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<GeneratedDocumentDto> updateGeneratedDocument(@PathVariable Long id,
            @Valid @RequestBody GeneratedDocumentDto generatedDocumentDto) {
        GeneratedDocumentDto updatedGeneratedDocument = generatedDocumentService.updateGeneratedDocument(id,
                generatedDocumentDto);
        return new ResponseEntity<>(updatedGeneratedDocument, HttpStatus.OK);
    }

    @PutMapping("/generated-documents/updateStatus/{id}/{status}")
    @Operation(summary = "Update generated document status", description = "Update generated document status")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Generated document status updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Generated document not found")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<GeneratedDocumentDto> updateGeneratedDocumentStatus(@PathVariable Long id,
            @PathVariable String status) {
        GeneratedDocumentDto updatedGeneratedDocument = generatedDocumentService.updateGeneratedDocumentStatus(id,
                status);
        return new ResponseEntity<>(updatedGeneratedDocument, HttpStatus.OK);
    }

    @DeleteMapping("/generated-documents/deleteById/{id}")
    @Operation(summary = "Delete generated document", description = "Delete generated document")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Generated document deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or generated document is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Generated document not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteGeneratedDocument(@PathVariable Long id) {
        generatedDocumentService.deleteGeneratedDocument(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
