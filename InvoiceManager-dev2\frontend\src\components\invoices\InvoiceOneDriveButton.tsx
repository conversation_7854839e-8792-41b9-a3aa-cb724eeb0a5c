import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Cloud, CloudUpload, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import oneDriveService, { OneDriveUploadResponse } from '@/services/oneDriveService';
import { fetchInvoicePdfBlob, generateInvoicePdfBlob } from '@/utils/pdfUtils';
import { Invoice } from '@/types/invoice';

interface InvoiceOneDriveButtonProps {
  invoice: Invoice;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  onUploadSuccess?: (response: OneDriveUploadResponse) => void;
  onUploadError?: (error: string) => void;
}

const InvoiceOneDriveButton: React.FC<InvoiceOneDriveButtonProps> = ({
  invoice,
  variant = 'ghost',
  size = 'sm',
  className = '',
  onUploadSuccess,
  onUploadError
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleUpload = async () => {
    setIsUploading(true);
    setUploadStatus('idle');

    try {
      console.log('OneDrive upload started for invoice:', invoice.id);

      // Check if user is authenticated
      const authStatus = await oneDriveService.checkAuthentication();
      console.log('Authentication status:', authStatus);

      if (!authStatus.authenticated) {
        console.log('User not authenticated, starting authentication flow...');

        // Show authentication options to user
        const authChoice = window.confirm(
          'OneDrive Authentication Required\n\n' +
          'Choose authentication method:\n' +
          'OK = Quick authentication (popup)\n' +
          'Cancel = Alternative method (device code)\n\n' +
          'If you see authentication errors, try the alternative method.'
        );

        let authResult;

        if (authChoice) {
          // Try popup authentication first
          toast.info('Opening OneDrive authentication...', {
            description: 'A popup window will open for sign-in.'
          });

          authResult = await oneDriveService.authenticate();

          // If popup fails due to redirect URI issue, show helpful message
          if (!authResult.success) {
            if (authResult.error?.includes('AADSTS50011') || authResult.error?.includes('redirect_uri')) {
              toast.error('Authentication Configuration Issue', {
                description: 'The redirect URI needs to be registered in Azure Portal. Using alternative method...'
              });

              // Automatically try device code as fallback
              authResult = await oneDriveService.authenticateWithDeviceCode();
            } else if (authResult.error?.includes('popup')) {
              toast.info('Popup blocked. Trying alternative authentication...', {
                description: 'Please follow the device code instructions.'
              });
              authResult = await oneDriveService.authenticateWithDeviceCode();
            }
          }
        } else {
          // Use device code authentication
          toast.info('Starting device code authentication...', {
            description: 'This method works without popup windows.'
          });
          authResult = await oneDriveService.authenticateWithDeviceCode();
        }

        if (!authResult.success) {
          throw new Error(authResult.error || 'Authentication failed');
        }

        toast.success('Successfully authenticated with OneDrive!', {
          description: 'You can now save invoices to OneDrive.'
        });
      }

      // Generate PDF blob
      let pdfBlob: Blob;

      try {
        console.log('🔄 Attempting to fetch PDF for invoice:', invoice.id);

        // First, let's debug the invoice data
        try {
          const debugResponse = await fetch(`/api/invoice-generation/debug/${invoice.id}`);
          if (debugResponse.ok) {
            const debugData = await debugResponse.json();
            console.log('🔍 Invoice debug data:', debugData);
          }
        } catch (debugError) {
          console.warn('Debug endpoint failed:', debugError);
        }

        // Use the database ID for the PDF endpoint, not the display ID
        const invoiceId = invoice.databaseId || invoice.id;
        console.log('📄 Using invoice ID for PDF:', invoiceId);

        // Use the direct PDF endpoint with database ID
        const pdfUrl = `/api/invoice-generation/pdf/${invoiceId}`;
        console.log('📄 Using working PDF URL:', pdfUrl);

        // Fetch the PDF as a blob
        const response = await fetch(pdfUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
          }
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`❌ PDF fetch failed: ${response.status} ${response.statusText}`, errorText);
          throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
        }

        pdfBlob = await response.blob();
        console.log('✅ Successfully fetched PDF, size:', pdfBlob.size, 'bytes');

        // Validate that the PDF is not empty or corrupted
        if (pdfBlob.size < 1000) {
          console.warn('⚠️ PDF appears to be too small:', pdfBlob.size, 'bytes');
          throw new Error('PDF appears to be too small or corrupted');
        }
      } catch (backendError) {
        console.error('❌ PDF fetch failed:', backendError);
        throw new Error('PDF generation failed. Please ensure the invoice data is complete and try again.');
      }

      // Upload to OneDrive
      console.log('Uploading PDF to OneDrive, blob size:', pdfBlob.size, 'bytes');
      const response = await oneDriveService.uploadPdf(pdfBlob, invoice.id);
      console.log('OneDrive upload response:', response);

      if (response.success) {
        setUploadStatus('success');
        console.log('OneDrive upload successful:', response);
        toast.success('Invoice PDF saved to OneDrive!', {
          description: `File: ${response.fileName}`,
          action: response.webUrl ? {
            label: 'Open in OneDrive',
            onClick: () => window.open(response.webUrl, '_blank')
          } : undefined
        });

        if (onUploadSuccess) {
          onUploadSuccess(response);
        }
      } else {
        setUploadStatus('error');
        const errorMessage = response.error || response.message || 'Upload failed';
        console.error('OneDrive upload failed:', errorMessage);
        toast.error('Failed to save to OneDrive', {
          description: errorMessage
        });

        if (onUploadError) {
          onUploadError(errorMessage);
        }
      }
    } catch (error) {
      setUploadStatus('error');
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      console.error('OneDrive upload error:', error);
      
      toast.error('Failed to save to OneDrive', {
        description: errorMessage
      });
      
      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setIsUploading(false);
      // Reset status after 3 seconds
      setTimeout(() => setUploadStatus('idle'), 3000);
    }
  };

  const getButtonIcon = () => {
    if (isUploading) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    
    if (uploadStatus === 'success') {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    
    if (uploadStatus === 'error') {
      return <AlertCircle className="h-4 w-4 text-red-600" />;
    }
    
    return <CloudUpload className="h-4 w-4" />;
  };

  const getButtonText = () => {
    if (isUploading) {
      return 'Saving...';
    }

    if (uploadStatus === 'success') {
      return 'Saved';
    }

    if (uploadStatus === 'error') {
      return 'Failed';
    }

    return 'Save to OneDrive';
  };

  const getButtonVariant = () => {
    if (uploadStatus === 'success') {
      return 'default';
    }
    
    if (uploadStatus === 'error') {
      return 'destructive';
    }
    
    return variant;
  };

  return (
    <Button
      onClick={handleUpload}
      disabled={isUploading}
      variant={getButtonVariant()}
      size={size}
      className={`${className} transition-all duration-200`}
      title={`Save Invoice ${invoice.id} to OneDrive`}
    >
      {getButtonIcon()}
      {size !== 'icon' && <span className="ml-1">{getButtonText()}</span>}
    </Button>
  );
};

export default InvoiceOneDriveButton;
