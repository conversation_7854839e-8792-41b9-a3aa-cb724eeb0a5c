import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, XCircle, ExternalLink } from 'lucide-react';
import { toast } from 'sonner';

const AzureConfigTest: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);

  const runAzureTest = async () => {
    setIsRunning(true);
    setTestResult(null);

    try {
      console.clear();
      console.log('🔍 Testing Azure Configuration...');

      // Test device code generation
      const response = await fetch('/api/onedrive/device-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        let solution = '';

        if (response.status === 400) {
          errorMessage = 'Bad Request - Azure app configuration issue';
          solution = 'Your Azure app needs to be configured to allow public client flows. Go to Azure Portal → App registrations → Your app → Authentication → Allow public client flows = YES';
        } else if (response.status === 401) {
          errorMessage = 'Unauthorized - Invalid client credentials';
          solution = 'Check your Client ID and Tenant ID in application.properties. Make sure they match your Azure app registration.';
        } else if (response.status === 403) {
          errorMessage = 'Forbidden - Insufficient permissions';
          solution = 'Your Azure app needs API permissions. Go to Azure Portal → App registrations → Your app → API permissions → Add Microsoft Graph Files.ReadWrite.All';
        }

        setTestResult({
          success: false,
          error: errorMessage,
          solution: solution,
          details: errorText
        });

        toast.error('Azure Configuration Test Failed', {
          description: errorMessage
        });
        return;
      }

      const data = await response.json();
      console.log('Success response:', data);

      if (data.success && data.device_code) {
        setTestResult({
          success: true,
          message: 'Azure configuration is working correctly!',
          userCode: data.user_code,
          verificationUri: data.verification_uri,
          details: 'Device code flow is properly configured'
        });

        toast.success('Azure Configuration Test Passed!', {
          description: 'Your Azure app is configured correctly for OneDrive integration.'
        });
      } else {
        setTestResult({
          success: false,
          error: 'Unexpected response format',
          details: JSON.stringify(data, null, 2)
        });

        toast.error('Unexpected Response', {
          description: 'Got response but not in expected format'
        });
      }

    } catch (error) {
      console.error('Test failed:', error);
      
      setTestResult({
        success: false,
        error: 'Network or server error',
        solution: 'Make sure your backend is running and accessible',
        details: error instanceof Error ? error.message : String(error)
      });

      toast.error('Test Failed', {
        description: 'Could not connect to backend or server error'
      });
    } finally {
      setIsRunning(false);
    }
  };

  const openAzurePortal = () => {
    window.open('https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationsListBlade', '_blank');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-blue-500" />
          Azure Configuration Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-3">
          <Button
            onClick={runAzureTest}
            disabled={isRunning}
            variant="outline"
            className="gap-2"
          >
            {isRunning ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <AlertTriangle className="h-4 w-4" />
            )}
            {isRunning ? 'Testing...' : 'Test Azure Configuration'}
          </Button>

          <Button
            onClick={openAzurePortal}
            variant="outline"
            className="gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            Open Azure Portal
          </Button>
        </div>

        {testResult && (
          <div className="space-y-3">
            <div className={`border rounded-lg p-4 ${testResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
              <div className="flex items-center gap-2 mb-2">
                {testResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
                <span className="font-semibold">
                  {testResult.success ? 'Test Passed' : 'Test Failed'}
                </span>
              </div>
              
              <p className="mb-2">
                {testResult.success ? testResult.message : testResult.error}
              </p>

              {testResult.success && testResult.userCode && (
                <div className="bg-white border rounded p-3 mt-3">
                  <p className="text-sm font-medium">Test Device Code Generated:</p>
                  <p className="text-lg font-mono">{testResult.userCode}</p>
                  <p className="text-sm text-gray-600">Verification URL: {testResult.verificationUri}</p>
                </div>
              )}

              {testResult.solution && (
                <div className="bg-blue-50 border border-blue-200 rounded p-3 mt-3">
                  <p className="font-semibold text-blue-900 mb-1">Solution:</p>
                  <p className="text-blue-800 text-sm">{testResult.solution}</p>
                </div>
              )}

              {testResult.details && (
                <details className="mt-3">
                  <summary className="cursor-pointer text-sm font-medium">Technical Details</summary>
                  <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-auto">
                    {testResult.details}
                  </pre>
                </details>
              )}
            </div>
          </div>
        )}

        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Required Azure Configuration:</strong>
            <ol className="list-decimal list-inside mt-2 space-y-1 text-sm">
              <li>Go to Azure Portal → App registrations → Your app</li>
              <li><strong>Authentication</strong> → Set "Allow public client flows" to <strong>YES</strong></li>
              <li><strong>API permissions</strong> → Add Microsoft Graph → Files.ReadWrite.All (Delegated)</li>
              <li><strong>Grant admin consent</strong> for the permissions</li>
            </ol>
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default AzureConfigTest;
