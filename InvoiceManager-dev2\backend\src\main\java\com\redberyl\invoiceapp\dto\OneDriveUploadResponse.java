package com.redberyl.invoiceapp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for OneDrive upload response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OneDriveUploadResponse {
    
    private boolean success;
    private String message;
    private String fileId;
    private String fileName;
    private String webUrl;
    private String downloadUrl;
    private Long fileSize;
    private String uploadedAt;
    private String error;
    
    public static OneDriveUploadResponse success(String fileId, String fileName, String webUrl, String downloadUrl, Long fileSize) {
        return OneDriveUploadResponse.builder()
                .success(true)
                .message("File uploaded successfully to OneDrive")
                .fileId(fileId)
                .fileName(fileName)
                .webUrl(webUrl)
                .downloadUrl(downloadUrl)
                .fileSize(fileSize)
                .uploadedAt(java.time.LocalDateTime.now().toString())
                .build();
    }
    
    public static OneDriveUploadResponse error(String error) {
        return OneDriveUploadResponse.builder()
                .success(false)
                .message("Failed to upload file to OneDrive")
                .error(error)
                .build();
    }
}
